using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Shared.Interfaces;

namespace Shared.Models.FileStorage
{
    public class FileThumbnail : IAuditableEntity, ISoftDeletable
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        public Guid FileRecordId { get; set; }

        [Required]
        [MaxLength(255)]
        public string ThumbnailFileName { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string ThumbnailObjectKey { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string BucketName { get; set; } = string.Empty;

        public int Width { get; set; }
        public int Height { get; set; }
        public long SizeBytes { get; set; }

        [MaxLength(100)]
        public string? ETag { get; set; }

        [MaxLength(1000)]
        public string? AccessUrl { get; set; }

        // Navigation Properties
        [ForeignKey(nameof(FileRecordId))]
        public virtual FileRecord FileRecord { get; set; } = null!;

        // IAuditableEntity implementation
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // ISoftDeletable implementation
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}
