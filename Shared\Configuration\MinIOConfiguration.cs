namespace Shared.Configuration
{
    public class MinIOConfiguration
    {
        public const string SectionName = "MinIO";

        public string Endpoint { get; set; } = string.Empty;
        public string AccessKey { get; set; } = string.Empty;
        public string SecretKey { get; set; } = string.Empty;
        public bool UseSSL { get; set; } = false;
        public string Region { get; set; } = "us-east-1";
        public int ConnectionTimeout { get; set; } = 30000; // 30 seconds
        public int RequestTimeout { get; set; } = 300000; // 5 minutes
    }

    public class FileStorageConfiguration
    {
        public const string SectionName = "FileStorage";

        public MinIOConfiguration MinIO { get; set; } = new();
        public BucketConfiguration Buckets { get; set; } = new();
        public FileValidationConfiguration Validation { get; set; } = new();
        public ThumbnailConfiguration Thumbnails { get; set; } = new();
        public bool EnableEncryption { get; set; } = false;
        public string EncryptionKey { get; set; } = string.Empty;
    }

    public class BucketConfiguration
    {
        public string ProfilePictures { get; set; } = "profile-pictures";
        public string Certifications { get; set; } = "certifications";
        public string Signatures { get; set; } = "signatures";
        public string Temp { get; set; } = "temp";
        public string Documents { get; set; } = "docs";

        public string[] GetAllBuckets()
        {
            return new[] { ProfilePictures, Certifications, Signatures, Temp, Documents };
        }
    }

    public class FileValidationConfiguration
    {
        public long MaxFileSizeBytes { get; set; } = 50 * 1024 * 1024; // 50MB default
        public string[] AllowedImageTypes { get; set; } = 
        {
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", 
            "image/webp", "image/svg+xml", "image/tiff"
        };
        public string[] AllowedDocumentTypes { get; set; } = 
        {
            "application/pdf",
            "application/msword",
            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
            "application/vnd.ms-excel",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            "application/vnd.ms-powerpoint",
            "application/vnd.openxmlformats-officedocument.presentationml.presentation",
            "text/plain",
            "text/csv"
        };

        public string[] GetAllAllowedTypes()
        {
            return AllowedImageTypes.Concat(AllowedDocumentTypes).ToArray();
        }
    }

    public class ThumbnailConfiguration
    {
        public bool EnableThumbnails { get; set; } = true;
        public int ThumbnailWidth { get; set; } = 200;
        public int ThumbnailHeight { get; set; } = 200;
        public int ThumbnailQuality { get; set; } = 85;
        public string ThumbnailSuffix { get; set; } = "_thumb";
        public string[] SupportedImageTypes { get; set; } = 
        {
            "image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp"
        };
    }
}
