using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Shared.Exceptions;
using Shared.Interfaces;
using Shared.Models.FileStorage;

namespace Shared.Services
{
    public class FileStorageTransactionService : IFileStorageTransactionService
    {
        private readonly IFileStorageService _fileStorageService;
        private readonly IDbContextFactory<DbContext> _dbContextFactory;
        private readonly ILogger<FileStorageTransactionService> _logger;

        public FileStorageTransactionService(
            IFileStorageService fileStorageService,
            IDbContextFactory<DbContext> dbContextFactory,
            ILogger<FileStorageTransactionService> logger)
        {
            _fileStorageService = fileStorageService;
            _dbContextFactory = dbContextFactory;
            _logger = logger;
        }

        /// <summary>
        /// Executes a transaction that coordinates both file storage operations and database operations
        /// </summary>
        /// <typeparam name="T">The type of the result</typeparam>
        /// <param name="fileOperations">File operations to perform</param>
        /// <param name="databaseOperations">Database operations to perform</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The result of the transaction</returns>
        public async Task<T> ExecuteTransactionAsync<T>(
            Func<IFileStorageService, Task<List<FileUploadResult>>> fileOperations,
            Func<DbContext, List<FileUploadResult>, Task<T>> databaseOperations,
            CancellationToken cancellationToken = default)
        {
            var transactionId = Guid.NewGuid();
            var uploadedFiles = new List<FileUploadResult>();

            _logger.LogInformation("Starting transaction {TransactionId}", transactionId);

            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
            await using var dbTransaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

            try
            {
                // Execute file operations first
                uploadedFiles = await fileOperations(_fileStorageService);
                _logger.LogInformation("Completed file operations for transaction {TransactionId}, uploaded {FileCount} files", 
                    transactionId, uploadedFiles.Count);

                // Execute database operations
                var result = await databaseOperations(dbContext, uploadedFiles);
                _logger.LogInformation("Completed database operations for transaction {TransactionId}", transactionId);

                // Commit the database transaction
                await dbTransaction.CommitAsync(cancellationToken);
                _logger.LogInformation("Successfully committed transaction {TransactionId}", transactionId);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Transaction {TransactionId} failed, rolling back", transactionId);

                // Rollback database transaction
                await dbTransaction.RollbackAsync(cancellationToken);

                // Cleanup uploaded files from MinIO
                await CleanupUploadedFilesAsync(uploadedFiles, cancellationToken);

                throw new FileStorageTransactionException(transactionId, ex.Message, ex);
            }
        }

        /// <summary>
        /// Executes a simple transaction with file uploads and database operations
        /// </summary>
        /// <param name="fileUploads">List of file upload operations</param>
        /// <param name="databaseOperations">Database operations to perform after file uploads</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Transaction result</returns>
        public async Task<FileStorageTransactionResult> ExecuteSimpleTransactionAsync(
            List<(Stream stream, FileUploadRequest request)> fileUploads,
            Func<DbContext, List<FileUploadResult>, Task> databaseOperations,
            CancellationToken cancellationToken = default)
        {
            var transactionId = Guid.NewGuid();
            var result = new FileStorageTransactionResult
            {
                TransactionId = transactionId
            };

            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
            await using var dbTransaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

            try
            {
                // Upload files
                foreach (var (stream, request) in fileUploads)
                {
                    var uploadResult = await _fileStorageService.UploadFileAsync(stream, request, cancellationToken);
                    result.UploadResults.Add(uploadResult);
                }

                // Execute database operations
                await databaseOperations(dbContext, result.UploadResults);

                // Commit transaction
                await dbTransaction.CommitAsync(cancellationToken);
                result.Success = true;

                _logger.LogInformation("Successfully completed transaction {TransactionId} with {FileCount} files", 
                    transactionId, result.UploadResults.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Transaction {TransactionId} failed", transactionId);

                // Rollback database
                await dbTransaction.RollbackAsync(cancellationToken);

                // Cleanup files
                await CleanupUploadedFilesAsync(result.UploadResults, cancellationToken);

                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Exception = ex;
            }

            return result;
        }

        /// <summary>
        /// Creates a transaction scope for coordinating file operations with external database operations
        /// </summary>
        /// <param name="isolationLevel">Transaction isolation level</param>
        /// <returns>A transaction scope that can be used with external DbContext</returns>
        public async Task<IFileStorageTransactionScope> CreateTransactionScopeAsync(
            System.Data.IsolationLevel isolationLevel = System.Data.IsolationLevel.ReadCommitted)
        {
            return new FileStorageTransactionScope(_fileStorageService, _dbContextFactory, _logger, isolationLevel);
        }

        private async Task CleanupUploadedFilesAsync(List<FileUploadResult> uploadedFiles, CancellationToken cancellationToken)
        {
            foreach (var uploadResult in uploadedFiles)
            {
                try
                {
                    await _fileStorageService.DeleteFileAsync(uploadResult.FileId, hardDelete: true, cancellationToken);
                    _logger.LogInformation("Cleaned up file {FileId} after transaction failure", uploadResult.FileId);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to cleanup file {FileId} after transaction failure", uploadResult.FileId);
                }
            }
        }
    }



    internal class FileStorageTransactionScope : IFileStorageTransactionScope
    {
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger _logger;
        private readonly List<FileUploadResult> _uploadedFiles = new();
        private readonly List<Guid> _deletedFiles = new();
        private bool _committed = false;
        private bool _disposed = false;

        public DbContext DbContext { get; }
        private readonly Microsoft.EntityFrameworkCore.Storage.IDbContextTransaction _dbTransaction;

        public FileStorageTransactionScope(
            IFileStorageService fileStorageService,
            IDbContextFactory<DbContext> dbContextFactory,
            ILogger logger,
            System.Data.IsolationLevel isolationLevel)
        {
            _fileStorageService = fileStorageService;
            _logger = logger;
            DbContext = dbContextFactory.CreateDbContext();
            _dbTransaction = DbContext.Database.BeginTransaction();
        }

        public async Task<FileUploadResult> UploadFileAsync(Stream fileStream, FileUploadRequest request, CancellationToken cancellationToken = default)
        {
            var result = await _fileStorageService.UploadFileAsync(fileStream, request, cancellationToken);
            _uploadedFiles.Add(result);
            return result;
        }

        public async Task<bool> DeleteFileAsync(Guid fileId, CancellationToken cancellationToken = default)
        {
            var result = await _fileStorageService.DeleteFileAsync(fileId, true, cancellationToken);
            if (result)
            {
                _deletedFiles.Add(fileId);
            }
            return result;
        }

        public async Task CommitAsync(CancellationToken cancellationToken = default)
        {
            await _dbTransaction.CommitAsync(cancellationToken);
            _committed = true;
        }

        public async Task RollbackAsync(CancellationToken cancellationToken = default)
        {
            await _dbTransaction.RollbackAsync(cancellationToken);
            
            // Cleanup uploaded files
            foreach (var uploadResult in _uploadedFiles)
            {
                try
                {
                    await _fileStorageService.DeleteFileAsync(uploadResult.FileId, hardDelete: true, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to cleanup file {FileId} after rollback", uploadResult.FileId);
                }
            }
        }

        public async ValueTask DisposeAsync()
        {
            if (!_disposed)
            {
                if (!_committed)
                {
                    await RollbackAsync();
                }

                await _dbTransaction.DisposeAsync();
                await DbContext.DisposeAsync();
                _disposed = true;
            }
        }
    }
}
