using Microsoft.EntityFrameworkCore;
using Shared.Models.FileStorage;

namespace Shared.Interfaces
{
    public interface IFileStorageTransactionService
    {
        /// <summary>
        /// Executes a transaction that coordinates both file storage operations and database operations
        /// </summary>
        /// <typeparam name="T">The type of the result</typeparam>
        /// <param name="fileOperations">File operations to perform</param>
        /// <param name="databaseOperations">Database operations to perform</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The result of the transaction</returns>
        Task<T> ExecuteTransactionAsync<T>(
            Func<IFileStorageService, Task<List<FileUploadResult>>> fileOperations,
            Func<DbContext, List<FileUploadResult>, Task<T>> databaseOperations,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Executes a simple transaction with file uploads and database operations
        /// </summary>
        /// <param name="fileUploads">List of file upload operations</param>
        /// <param name="databaseOperations">Database operations to perform after file uploads</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Transaction result</returns>
        Task<FileStorageTransactionResult> ExecuteSimpleTransactionAsync(
            List<(Stream stream, FileUploadRequest request)> fileUploads,
            Func<DbContext, List<FileUploadResult>, Task> databaseOperations,
            CancellationToken cancellationToken = default);

        /// <summary>
        /// Creates a transaction scope for coordinating file operations with external database operations
        /// </summary>
        /// <param name="isolationLevel">Transaction isolation level</param>
        /// <returns>A transaction scope that can be used with external DbContext</returns>
        Task<IFileStorageTransactionScope> CreateTransactionScopeAsync(
            System.Data.IsolationLevel isolationLevel = System.Data.IsolationLevel.ReadCommitted);
    }

    public interface IFileStorageTransactionScope : IAsyncDisposable
    {
        Task<FileUploadResult> UploadFileAsync(Stream fileStream, FileUploadRequest request, CancellationToken cancellationToken = default);
        Task<bool> DeleteFileAsync(Guid fileId, CancellationToken cancellationToken = default);
        Task CommitAsync(CancellationToken cancellationToken = default);
        Task RollbackAsync(CancellationToken cancellationToken = default);
        DbContext DbContext { get; }
    }
}
