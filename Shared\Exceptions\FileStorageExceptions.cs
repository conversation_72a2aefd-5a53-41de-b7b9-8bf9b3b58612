namespace Shared.Exceptions
{
    public class FileStorageException : Exception
    {
        public FileStorageException(string message) : base(message) { }
        public FileStorageException(string message, Exception innerException) : base(message, innerException) { }
    }

    public class FileNotFoundException : FileStorageException
    {
        public Guid FileId { get; }

        public FileNotFoundException(Guid fileId) 
            : base($"File with ID '{fileId}' was not found.")
        {
            FileId = fileId;
        }

        public FileNotFoundException(Guid fileId, string message) 
            : base(message)
        {
            FileId = fileId;
        }
    }

    public class InvalidFileTypeException : FileStorageException
    {
        public string ContentType { get; }
        public string[] AllowedTypes { get; }

        public InvalidFileTypeException(string contentType, string[] allowedTypes)
            : base($"File type '{contentType}' is not allowed. Allowed types: {string.Join(", ", allowedTypes)}")
        {
            ContentType = contentType;
            AllowedTypes = allowedTypes;
        }
    }

    public class FileSizeExceededException : FileStorageException
    {
        public long FileSize { get; }
        public long MaxAllowedSize { get; }

        public FileSizeExceededException(long fileSize, long maxAllowedSize)
            : base($"File size {fileSize} bytes exceeds maximum allowed size of {maxAllowedSize} bytes.")
        {
            FileSize = fileSize;
            MaxAllowedSize = maxAllowedSize;
        }
    }

    public class BucketNotFoundException : FileStorageException
    {
        public string BucketName { get; }

        public BucketNotFoundException(string bucketName)
            : base($"Bucket '{bucketName}' was not found.")
        {
            BucketName = bucketName;
        }
    }

    public class InvalidBucketNameException : FileStorageException
    {
        public string BucketName { get; }

        public InvalidBucketNameException(string bucketName)
            : base($"Bucket name '{bucketName}' is not valid.")
        {
            BucketName = bucketName;
        }
    }

    public class FileStorageTransactionException : FileStorageException
    {
        public Guid TransactionId { get; }

        public FileStorageTransactionException(Guid transactionId, string message)
            : base($"Transaction {transactionId} failed: {message}")
        {
            TransactionId = transactionId;
        }

        public FileStorageTransactionException(Guid transactionId, string message, Exception innerException)
            : base($"Transaction {transactionId} failed: {message}", innerException)
        {
            TransactionId = transactionId;
        }
    }

    public class ThumbnailGenerationException : FileStorageException
    {
        public Guid FileId { get; }

        public ThumbnailGenerationException(Guid fileId, string message)
            : base($"Failed to generate thumbnail for file {fileId}: {message}")
        {
            FileId = fileId;
        }

        public ThumbnailGenerationException(Guid fileId, string message, Exception innerException)
            : base($"Failed to generate thumbnail for file {fileId}: {message}", innerException)
        {
            FileId = fileId;
        }
    }
}
