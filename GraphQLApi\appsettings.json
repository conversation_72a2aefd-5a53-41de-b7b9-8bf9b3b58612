{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=WorkforceManagement_3;Trusted_Connection=True;TrustServerCertificate=True;MultipleActiveResultSets=true"}, "HikvisionApi": {"BaseUrl": "https://your-hikvision-api-url", "ApiKey": "your-api-key-here"}, "PhotoStorage": {"LocalPath": "wwwroot/photos"}, "FileStorage": {"MinIO": {"Endpoint": "localhost:9000", "AccessKey": "minioadmin", "SecretKey": "minioadmin", "UseSSL": false, "Region": "us-east-1", "ConnectionTimeout": 30000, "RequestTimeout": 300000}, "Buckets": {"ProfilePictures": "profile-pictures", "Certifications": "certifications", "Signatures": "signatures", "Temp": "temp", "Documents": "docs"}, "Validation": {"MaxFileSizeBytes": 52428800, "AllowedImageTypes": ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp", "image/svg+xml", "image/tiff"], "AllowedDocumentTypes": ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.ms-powerpoint", "application/vnd.openxmlformats-officedocument.presentationml.presentation", "text/plain", "text/csv"]}, "Thumbnails": {"EnableThumbnails": true, "ThumbnailWidth": 200, "ThumbnailHeight": 200, "ThumbnailQuality": 85, "ThumbnailSuffix": "_thumb", "SupportedImageTypes": ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp"]}, "EnableEncryption": false, "EncryptionKey": ""}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}