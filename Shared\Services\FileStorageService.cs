using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Minio;
using Minio.DataModel.Args;
using Shared.Configuration;
using Shared.Constants;
using Shared.Exceptions;
using Shared.Interfaces;
using Shared.Models.FileStorage;
using System.Text;
using System.Text.Json;
using FileNotFoundException = Shared.Exceptions.FileNotFoundException;

namespace Shared.Services
{
    public class FileStorageService : IFileStorageService
    {
        private readonly IMinioClient _minioClient;
        private readonly IDbContextFactory<DbContext> _dbContextFactory;
        private readonly ILogger<FileStorageService> _logger;
        private readonly FileStorageConfiguration _config;
        private readonly IThumbnailService _thumbnailService;

        public FileStorageService(
            IMinioClient minioClient,
            IDbContextFactory<DbContext> dbContextFactory,
            ILogger<FileStorageService> logger,
            IOptions<FileStorageConfiguration> config,
            IThumbnailService thumbnailService)
        {
            _minioClient = minioClient;
            _dbContextFactory = dbContextFactory;
            _logger = logger;
            _config = config.Value;
            _thumbnailService = thumbnailService;
        }

        public async Task<FileUploadResult> UploadFileAsync(Stream fileStream, FileUploadRequest request, CancellationToken cancellationToken = default)
        {
            if (fileStream == null || !fileStream.CanRead)
                throw new ArgumentException("File stream must be readable", nameof(fileStream));

            if (string.IsNullOrEmpty(request.FileName))
                throw new ArgumentException("File name is required", nameof(request));

            if (!BucketNames.IsValidBucket(request.BucketName))
                throw new InvalidBucketNameException(request.BucketName);

            // Validate file
            await ValidateFileAsync(fileStream, request.FileName, cancellationToken);

            // Detect content type
            var contentType = await FileTypeDetectionService.DetectMimeTypeAsync(fileStream, request.FileName);
            
            // Generate object key
            var objectKey = GenerateObjectKey(request.BucketName, request.FileName, request.FolderPath);
            
            // Ensure bucket exists
            await EnsureBucketExistsAsync(request.BucketName, cancellationToken);

            var fileId = Guid.NewGuid();
            var fileSize = fileStream.Length;

            try
            {
                // Upload to MinIO
                var putObjectArgs = new PutObjectArgs()
                    .WithBucket(request.BucketName)
                    .WithObject(objectKey)
                    .WithStreamData(fileStream)
                    .WithObjectSize(fileSize)
                    .WithContentType(contentType);

                if (request.Metadata != null && request.Metadata.Any())
                {
                    putObjectArgs.WithHeaders(request.Metadata);
                }

                var response = await _minioClient.PutObjectAsync(putObjectArgs, cancellationToken);

                // Create database record
                var fileRecord = new FileRecord
                {
                    Id = fileId,
                    FileName = request.FileName,
                    ContentType = contentType,
                    SizeBytes = fileSize,
                    Description = request.Description,
                    BucketName = request.BucketName,
                    ObjectKey = objectKey,
                    ETag = response.Etag,
                    Metadata = request.Metadata != null ? JsonSerializer.Serialize(request.Metadata) : null,
                    CurrentVersion = request.CreateNewVersion && request.OriginalFileId.HasValue ? await GetNextVersionNumberAsync(request.OriginalFileId.Value) : 1,
                    IsLatestVersion = true,
                    OriginalFileId = request.OriginalFileId
                };

                await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
                
                // If this is a new version, update the previous latest version
                if (request.CreateNewVersion && request.OriginalFileId.HasValue)
                {
                    await UpdatePreviousVersionsAsync(dbContext, request.OriginalFileId.Value);
                }

                dbContext.Set<FileRecord>().Add(fileRecord);
                await dbContext.SaveChangesAsync(cancellationToken);

                // Generate thumbnail if requested and file is an image
                var hasThumbnail = false;
                string? thumbnailUrl = null;
                
                if (request.GenerateThumbnail && FileTypeDetectionService.IsImage(contentType))
                {
                    try
                    {
                        hasThumbnail = await GenerateThumbnailAsync(fileId, cancellationToken);
                        if (hasThumbnail)
                        {
                            thumbnailUrl = GeneratePresignedUrlForThumbnailAsync(fileId, TimeSpan.FromHours(1));
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to generate thumbnail for file {FileId}", fileId);
                    }
                }

                return new FileUploadResult
                {
                    FileId = fileId,
                    FileName = request.FileName,
                    ObjectKey = objectKey,
                    BucketName = request.BucketName,
                    SizeBytes = fileSize,
                    ContentType = contentType,
                    ETag = response.Etag,
                    AccessUrl = await GeneratePresignedUrlAsync(fileId, TimeSpan.FromHours(1), cancellationToken),
                    Version = fileRecord.CurrentVersion,
                    HasThumbnail = hasThumbnail,
                    ThumbnailUrl = thumbnailUrl,
                    CreatedAt = fileRecord.CreatedAt
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload file {FileName} to bucket {BucketName}", request.FileName, request.BucketName);
                
                // Cleanup: try to remove the file from MinIO if it was uploaded
                try
                {
                    await _minioClient.RemoveObjectAsync(new RemoveObjectArgs()
                        .WithBucket(request.BucketName)
                        .WithObject(objectKey), cancellationToken);
                }
                catch (Exception cleanupEx)
                {
                    _logger.LogWarning(cleanupEx, "Failed to cleanup file {ObjectKey} from MinIO after upload failure", objectKey);
                }

                throw new FileStorageException($"Failed to upload file: {ex.Message}", ex);
            }
        }

        public async Task<FileDownloadResult> DownloadFileAsync(FileDownloadRequest request, CancellationToken cancellationToken = default)
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
            
            var query = dbContext.Set<FileRecord>().AsQueryable();
            
            if (request.Version.HasValue)
            {
                // Download specific version
                query = query.Where(f => f.OriginalFileId == request.FileId && f.CurrentVersion == request.Version.Value);
            }
            else
            {
                // Download latest version
                query = query.Where(f => f.Id == request.FileId || (f.OriginalFileId == request.FileId && f.IsLatestVersion));
            }

            var fileRecord = await query.FirstOrDefaultAsync(cancellationToken);
            
            if (fileRecord == null)
                throw new FileNotFoundException(request.FileId);

            try
            {
                var getObjectArgs = new GetObjectArgs()
                    .WithBucket(fileRecord.BucketName)
                    .WithObject(fileRecord.ObjectKey);

                var stream = new MemoryStream();
                await _minioClient.GetObjectAsync(getObjectArgs.WithCallbackStream(s => s.CopyTo(stream)), cancellationToken);
                stream.Position = 0;

                return new FileDownloadResult
                {
                    FileStream = stream,
                    FileName = fileRecord.FileName,
                    ContentType = fileRecord.ContentType,
                    SizeBytes = fileRecord.SizeBytes,
                    ETag = fileRecord.ETag
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to download file {FileId} from MinIO", request.FileId);
                throw new FileStorageException($"Failed to download file: {ex.Message}", ex);
            }
        }

        public async Task<FileMetadata?> GetFileMetadataAsync(Guid fileId, CancellationToken cancellationToken = default)
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
            
            var fileRecord = await dbContext.Set<FileRecord>()
                .Include(f => f.Thumbnails)
                .FirstOrDefaultAsync(f => f.Id == fileId, cancellationToken);

            if (fileRecord == null)
                return null;

            var metadata = new FileMetadata
            {
                Id = fileRecord.Id,
                FileName = fileRecord.FileName,
                ContentType = fileRecord.ContentType,
                SizeBytes = fileRecord.SizeBytes,
                Description = fileRecord.Description,
                BucketName = fileRecord.BucketName,
                ObjectKey = fileRecord.ObjectKey,
                CurrentVersion = fileRecord.CurrentVersion,
                IsLatestVersion = fileRecord.IsLatestVersion,
                OriginalFileId = fileRecord.OriginalFileId,
                AccessUrl = await GeneratePresignedUrlAsync(fileId, TimeSpan.FromHours(1), cancellationToken),
                CreatedAt = fileRecord.CreatedAt,
                CreatedBy = fileRecord.CreatedBy,
                UpdatedAt = fileRecord.UpdatedAt,
                UpdatedBy = fileRecord.UpdatedBy
            };

            // Parse metadata JSON
            if (!string.IsNullOrEmpty(fileRecord.Metadata))
            {
                try
                {
                    metadata.Metadata = JsonSerializer.Deserialize<Dictionary<string, string>>(fileRecord.Metadata);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Failed to parse metadata JSON for file {FileId}", fileId);
                }
            }

            // Add thumbnail information
            if (fileRecord.Thumbnails.Any())
            {
                metadata.Thumbnails = fileRecord.Thumbnails.Select(t => new ThumbnailInfo
                {
                    Id = t.Id,
                    FileName = t.ThumbnailFileName,
                    Width = t.Width,
                    Height = t.Height,
                    SizeBytes = t.SizeBytes,
                    AccessUrl = GeneratePresignedUrlForThumbnailSync(t.Id, TimeSpan.FromHours(1))
                }).ToList();
            }

            return metadata;
        }

        private string GenerateObjectKey(string bucketName, string fileName, string? folderPath = null)
        {
            var timestamp = DateTime.UtcNow.ToString("yyyy/MM/dd");
            var uniqueId = Guid.NewGuid().ToString("N")[..8];
            var sanitizedFileName = SanitizeFileName(fileName);
            
            if (!string.IsNullOrEmpty(folderPath))
            {
                folderPath = folderPath.Trim('/');
                return $"{folderPath}/{timestamp}/{uniqueId}_{sanitizedFileName}";
            }
            
            return $"{timestamp}/{uniqueId}_{sanitizedFileName}";
        }

        private static string SanitizeFileName(string fileName)
        {
            var invalidChars = Path.GetInvalidFileNameChars();
            return string.Join("_", fileName.Split(invalidChars, StringSplitOptions.RemoveEmptyEntries));
        }

        private async Task EnsureBucketExistsAsync(string bucketName, CancellationToken cancellationToken = default)
        {
            var bucketExistsArgs = new BucketExistsArgs().WithBucket(bucketName);
            var exists = await _minioClient.BucketExistsAsync(bucketExistsArgs, cancellationToken);
            
            if (!exists)
            {
                var makeBucketArgs = new MakeBucketArgs().WithBucket(bucketName);
                await _minioClient.MakeBucketAsync(makeBucketArgs, cancellationToken);
                _logger.LogInformation("Created bucket {BucketName}", bucketName);
            }
        }

        private async Task<int> GetNextVersionNumberAsync(Guid originalFileId)
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync();
            
            var maxVersion = await dbContext.Set<FileRecord>()
                .Where(f => f.OriginalFileId == originalFileId || f.Id == originalFileId)
                .MaxAsync(f => (int?)f.CurrentVersion);
                
            return (maxVersion ?? 0) + 1;
        }

        private async Task UpdatePreviousVersionsAsync(DbContext dbContext, Guid originalFileId)
        {
            var previousVersions = await dbContext.Set<FileRecord>()
                .Where(f => (f.OriginalFileId == originalFileId || f.Id == originalFileId) && f.IsLatestVersion)
                .ToListAsync();

            foreach (var version in previousVersions)
            {
                version.IsLatestVersion = false;
            }
        }

        public async Task<bool> DeleteFileAsync(Guid fileId, bool hardDelete = false, CancellationToken cancellationToken = default)
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);

            var fileRecord = await dbContext.Set<FileRecord>()
                .Include(f => f.Thumbnails)
                .FirstOrDefaultAsync(f => f.Id == fileId, cancellationToken);

            if (fileRecord == null)
                return false;

            try
            {
                if (hardDelete)
                {
                    // Delete from MinIO
                    await _minioClient.RemoveObjectAsync(new RemoveObjectArgs()
                        .WithBucket(fileRecord.BucketName)
                        .WithObject(fileRecord.ObjectKey), cancellationToken);

                    // Delete thumbnails from MinIO
                    foreach (var thumbnail in fileRecord.Thumbnails)
                    {
                        try
                        {
                            await _minioClient.RemoveObjectAsync(new RemoveObjectArgs()
                                .WithBucket(thumbnail.BucketName)
                                .WithObject(thumbnail.ThumbnailObjectKey), cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to delete thumbnail {ThumbnailId} from MinIO", thumbnail.Id);
                        }
                    }

                    // Hard delete from database
                    dbContext.Set<FileRecord>().Remove(fileRecord);
                }
                else
                {
                    // Soft delete
                    fileRecord.IsDeleted = true;
                    fileRecord.DeletedAt = DateTime.UtcNow;
                    fileRecord.DeletedBy = "System"; // TODO: Get current user
                }

                await dbContext.SaveChangesAsync(cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete file {FileId}", fileId);
                throw new FileStorageException($"Failed to delete file: {ex.Message}", ex);
            }
        }

        public async Task<FileListResult> ListFilesAsync(FileListRequest request, CancellationToken cancellationToken = default)
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);

            var query = dbContext.Set<FileRecord>().AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(request.BucketName))
                query = query.Where(f => f.BucketName == request.BucketName);

            if (!string.IsNullOrEmpty(request.FolderPath))
                query = query.Where(f => f.ObjectKey.StartsWith(request.FolderPath));

            if (!string.IsNullOrEmpty(request.FileNameFilter))
                query = query.Where(f => f.FileName.Contains(request.FileNameFilter));

            if (!string.IsNullOrEmpty(request.ContentTypeFilter))
                query = query.Where(f => f.ContentType.Contains(request.ContentTypeFilter));

            if (!request.IncludeVersions)
                query = query.Where(f => f.IsLatestVersion);

            // Include thumbnails if requested
            if (request.IncludeThumbnails)
                query = query.Include(f => f.Thumbnails);

            // Get total count
            var totalCount = await query.CountAsync(cancellationToken);

            // Apply sorting
            query = request.SortBy?.ToLowerInvariant() switch
            {
                "filename" => request.SortDescending ? query.OrderByDescending(f => f.FileName) : query.OrderBy(f => f.FileName),
                "size" => request.SortDescending ? query.OrderByDescending(f => f.SizeBytes) : query.OrderBy(f => f.SizeBytes),
                "contenttype" => request.SortDescending ? query.OrderByDescending(f => f.ContentType) : query.OrderBy(f => f.ContentType),
                "updatedat" => request.SortDescending ? query.OrderByDescending(f => f.UpdatedAt) : query.OrderBy(f => f.UpdatedAt),
                _ => request.SortDescending ? query.OrderByDescending(f => f.CreatedAt) : query.OrderBy(f => f.CreatedAt)
            };

            // Apply pagination
            var files = await query
                .Skip((request.PageNumber - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToListAsync(cancellationToken);

            var fileMetadataList = new List<FileMetadata>();
            foreach (var file in files)
            {
                var metadata = new FileMetadata
                {
                    Id = file.Id,
                    FileName = file.FileName,
                    ContentType = file.ContentType,
                    SizeBytes = file.SizeBytes,
                    Description = file.Description,
                    BucketName = file.BucketName,
                    ObjectKey = file.ObjectKey,
                    CurrentVersion = file.CurrentVersion,
                    IsLatestVersion = file.IsLatestVersion,
                    OriginalFileId = file.OriginalFileId,
                    AccessUrl = await GeneratePresignedUrlAsync(file.Id, TimeSpan.FromHours(1), cancellationToken),
                    CreatedAt = file.CreatedAt,
                    CreatedBy = file.CreatedBy,
                    UpdatedAt = file.UpdatedAt,
                    UpdatedBy = file.UpdatedBy
                };

                // Parse metadata JSON
                if (!string.IsNullOrEmpty(file.Metadata))
                {
                    try
                    {
                        metadata.Metadata = JsonSerializer.Deserialize<Dictionary<string, string>>(file.Metadata);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to parse metadata JSON for file {FileId}", file.Id);
                    }
                }

                // Add thumbnail information
                if (request.IncludeThumbnails && file.Thumbnails.Any())
                {
                    metadata.Thumbnails = file.Thumbnails.Select(t => new ThumbnailInfo
                    {
                        Id = t.Id,
                        FileName = t.ThumbnailFileName,
                        Width = t.Width,
                        Height = t.Height,
                        SizeBytes = t.SizeBytes,
                        AccessUrl = GeneratePresignedUrlForThumbnailSync(t.Id, TimeSpan.FromHours(1))
                    }).ToList();
                }

                fileMetadataList.Add(metadata);
            }

            return new FileListResult
            {
                Files = fileMetadataList,
                TotalCount = totalCount,
                PageNumber = request.PageNumber,
                PageSize = request.PageSize,
                TotalPages = (int)Math.Ceiling((double)totalCount / request.PageSize)
            };
        }

        public async Task<bool> ValidateFileAsync(Stream fileStream, string fileName, CancellationToken cancellationToken = default)
        {
            // Check file size
            if (!IsFileSizeAllowed(fileStream.Length))
                throw new FileSizeExceededException(fileStream.Length, _config.Validation.MaxFileSizeBytes);

            // Detect and validate content type
            var contentType = await FileTypeDetectionService.DetectMimeTypeAsync(fileStream, fileName);
            if (!IsFileTypeAllowed(contentType))
                throw new InvalidFileTypeException(contentType, _config.Validation.GetAllAllowedTypes());

            return true;
        }

        public bool IsFileTypeAllowed(string contentType)
        {
            return FileTypeDetectionService.IsFileTypeAllowed(contentType, _config.Validation.GetAllAllowedTypes());
        }

        public bool IsFileSizeAllowed(long sizeBytes)
        {
            return sizeBytes <= _config.Validation.MaxFileSizeBytes;
        }

        public async Task<bool> FileExistsAsync(Guid fileId, CancellationToken cancellationToken = default)
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
            return await dbContext.Set<FileRecord>().AnyAsync(f => f.Id == fileId, cancellationToken);
        }

        private string GeneratePresignedUrlForThumbnailSync(Guid thumbnailId, TimeSpan expiry)
        {
            // This is a simplified implementation - in a real scenario, you'd want to make this async
            // and properly handle the thumbnail URL generation
            return $"/files/files/thumbnails/{thumbnailId}";
        }

        private string GeneratePresignedUrlForThumbnailAsync(Guid fileId, TimeSpan expiry)
        {
            // Implementation for generating presigned URLs for thumbnails
            return $"/files/files/{fileId}/thumbnail";
        }

        // Versioning methods
        public async Task<FileUploadResult> CreateFileVersionAsync(Stream fileStream, Guid originalFileId, string? description = null, CancellationToken cancellationToken = default)
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);

            var originalFile = await dbContext.Set<FileRecord>()
                .FirstOrDefaultAsync(f => f.Id == originalFileId, cancellationToken);

            if (originalFile == null)
                throw new FileNotFoundException(originalFileId);

            var request = new FileUploadRequest
            {
                FileName = originalFile.FileName,
                BucketName = originalFile.BucketName,
                Description = description,
                CreateNewVersion = true,
                OriginalFileId = originalFileId,
                GenerateThumbnail = FileTypeDetectionService.IsImage(originalFile.ContentType)
            };

            return await UploadFileAsync(fileStream, request, cancellationToken);
        }

        public async Task<List<FileMetadata>> GetFileVersionsAsync(Guid originalFileId, CancellationToken cancellationToken = default)
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);

            var versions = await dbContext.Set<FileRecord>()
                .Where(f => f.Id == originalFileId || f.OriginalFileId == originalFileId)
                .OrderByDescending(f => f.CurrentVersion)
                .ToListAsync(cancellationToken);

            var result = new List<FileMetadata>();
            foreach (var version in versions)
            {
                var metadata = new FileMetadata
                {
                    Id = version.Id,
                    FileName = version.FileName,
                    ContentType = version.ContentType,
                    SizeBytes = version.SizeBytes,
                    Description = version.Description,
                    BucketName = version.BucketName,
                    ObjectKey = version.ObjectKey,
                    CurrentVersion = version.CurrentVersion,
                    IsLatestVersion = version.IsLatestVersion,
                    OriginalFileId = version.OriginalFileId,
                    AccessUrl = await GeneratePresignedUrlAsync(version.Id, TimeSpan.FromHours(1), cancellationToken),
                    CreatedAt = version.CreatedAt,
                    CreatedBy = version.CreatedBy,
                    UpdatedAt = version.UpdatedAt,
                    UpdatedBy = version.UpdatedBy
                };

                if (!string.IsNullOrEmpty(version.Metadata))
                {
                    try
                    {
                        metadata.Metadata = JsonSerializer.Deserialize<Dictionary<string, string>>(version.Metadata);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to parse metadata JSON for file {FileId}", version.Id);
                    }
                }

                result.Add(metadata);
            }

            return result;
        }

        public async Task<FileDownloadResult> DownloadFileVersionAsync(Guid originalFileId, int version, CancellationToken cancellationToken = default)
        {
            var request = new FileDownloadRequest
            {
                FileId = originalFileId,
                Version = version
            };

            return await DownloadFileAsync(request, cancellationToken);
        }

        // Bucket management methods
        public async Task<List<BucketInfo>> GetBucketsAsync(CancellationToken cancellationToken = default)
        {
            var buckets = await _minioClient.ListBucketsAsync(cancellationToken);
            var result = new List<BucketInfo>();

            foreach (var bucket in buckets.Buckets)
            {
                await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);

                var stats = await dbContext.Set<FileRecord>()
                    .Where(f => f.BucketName == bucket.Name)
                    .GroupBy(f => f.BucketName)
                    .Select(g => new { Count = g.Count(), TotalSize = g.Sum(f => f.SizeBytes) })
                    .FirstOrDefaultAsync(cancellationToken);

                result.Add(new BucketInfo
                {
                    Name = bucket.Name,
                    CreationDate = DateTime.TryParse(bucket.CreationDate, out var creationDate) ? creationDate : DateTime.UtcNow,
                    TotalFiles = stats?.Count ?? 0,
                    TotalSizeBytes = stats?.TotalSize ?? 0
                });
            }

            return result;
        }

        public async Task<bool> CreateBucketAsync(string bucketName, CancellationToken cancellationToken = default)
        {
            try
            {
                var makeBucketArgs = new MakeBucketArgs().WithBucket(bucketName);
                await _minioClient.MakeBucketAsync(makeBucketArgs, cancellationToken);
                _logger.LogInformation("Created bucket {BucketName}", bucketName);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create bucket {BucketName}", bucketName);
                return false;
            }
        }

        public async Task<bool> BucketExistsAsync(string bucketName, CancellationToken cancellationToken = default)
        {
            var bucketExistsArgs = new BucketExistsArgs().WithBucket(bucketName);
            return await _minioClient.BucketExistsAsync(bucketExistsArgs, cancellationToken);
        }

        // Folder operations
        public async Task<bool> CreateFolderAsync(string bucketName, string folderPath, CancellationToken cancellationToken = default)
        {
            try
            {
                // Ensure bucket exists
                await EnsureBucketExistsAsync(bucketName, cancellationToken);

                // Create a placeholder object to represent the folder
                var folderObjectKey = folderPath.TrimEnd('/') + "/.keep";
                var emptyStream = new MemoryStream(Encoding.UTF8.GetBytes(""));

                var putObjectArgs = new PutObjectArgs()
                    .WithBucket(bucketName)
                    .WithObject(folderObjectKey)
                    .WithStreamData(emptyStream)
                    .WithObjectSize(emptyStream.Length)
                    .WithContentType("text/plain");

                await _minioClient.PutObjectAsync(putObjectArgs, cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to create folder {FolderPath} in bucket {BucketName}", folderPath, bucketName);
                return false;
            }
        }

        public async Task<List<string>> ListFoldersAsync(string bucketName, string? parentPath = null, CancellationToken cancellationToken = default)
        {
            try
            {
                var listObjectsArgs = new ListObjectsArgs()
                    .WithBucket(bucketName)
                    .WithPrefix(parentPath ?? "");

                var folders = new List<string>();
                await foreach (var item in _minioClient.ListObjectsEnumAsync(listObjectsArgs, cancellationToken))
                {
                    if (item.IsDir)
                    {
                        folders.Add(item.Key);
                    }
                }

                return folders;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to list folders in bucket {BucketName}", bucketName);
                return new List<string>();
            }
        }

        // URL generation
        public async Task<string> GeneratePresignedUrlAsync(Guid fileId, TimeSpan expiry, CancellationToken cancellationToken = default)
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);

            var fileRecord = await dbContext.Set<FileRecord>()
                .FirstOrDefaultAsync(f => f.Id == fileId, cancellationToken);

            if (fileRecord == null)
                throw new FileNotFoundException(fileId);

            var presignedGetObjectArgs = new PresignedGetObjectArgs()
                .WithBucket(fileRecord.BucketName)
                .WithObject(fileRecord.ObjectKey)
                .WithExpiry((int)expiry.TotalSeconds);

            return await _minioClient.PresignedGetObjectAsync(presignedGetObjectArgs);
        }

        public async Task<string> GeneratePresignedUploadUrlAsync(string bucketName, string objectKey, TimeSpan expiry, CancellationToken cancellationToken = default)
        {
            var presignedPutObjectArgs = new PresignedPutObjectArgs()
                .WithBucket(bucketName)
                .WithObject(objectKey)
                .WithExpiry((int)expiry.TotalSeconds);

            return await _minioClient.PresignedPutObjectAsync(presignedPutObjectArgs);
        }

        public async Task<long> GetTotalStorageUsedAsync(string? bucketName = null, CancellationToken cancellationToken = default)
        {
            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);

            var query = dbContext.Set<FileRecord>().AsQueryable();

            if (!string.IsNullOrEmpty(bucketName))
                query = query.Where(f => f.BucketName == bucketName);

            return await query.SumAsync(f => f.SizeBytes, cancellationToken);
        }

        // Thumbnail methods
        public async Task<bool> GenerateThumbnailAsync(Guid fileId, CancellationToken cancellationToken = default)
        {
            return await _thumbnailService.GenerateThumbnailAsync(fileId, cancellationToken);
        }

        public async Task<FileDownloadResult?> DownloadThumbnailAsync(Guid fileId, CancellationToken cancellationToken = default)
        {
            return await _thumbnailService.GetThumbnailAsync(fileId, cancellationToken);
        }

        // Transaction support
        public async Task<FileStorageTransactionResult> ExecuteTransactionAsync(FileStorageTransaction transaction, CancellationToken cancellationToken = default)
        {
            var result = new FileStorageTransactionResult
            {
                TransactionId = transaction.TransactionId
            };

            await using var dbContext = await _dbContextFactory.CreateDbContextAsync(cancellationToken);
            await using var dbTransaction = await dbContext.Database.BeginTransactionAsync(cancellationToken);

            try
            {
                // Process file uploads
                foreach (var uploadRequest in transaction.FileUploads)
                {
                    // Note: In a real implementation, you'd need to pass the actual stream
                    // This is a simplified version for demonstration
                    throw new NotImplementedException("Transaction-based file uploads need stream handling");
                }

                // Process file deletions
                foreach (var fileId in transaction.FileDeletes)
                {
                    var deleted = await DeleteFileAsync(fileId, true, cancellationToken);
                    if (deleted)
                    {
                        result.DeletedFileIds.Add(fileId);
                    }
                }

                await dbTransaction.CommitAsync(cancellationToken);
                result.Success = true;
            }
            catch (Exception ex)
            {
                await dbTransaction.RollbackAsync(cancellationToken);
                result.Success = false;
                result.ErrorMessage = ex.Message;
                result.Exception = ex;

                _logger.LogError(ex, "Transaction {TransactionId} failed", transaction.TransactionId);

                // Cleanup any uploaded files from MinIO
                foreach (var uploadResult in result.UploadResults)
                {
                    try
                    {
                        await _minioClient.RemoveObjectAsync(new RemoveObjectArgs()
                            .WithBucket(uploadResult.BucketName)
                            .WithObject(uploadResult.ObjectKey), cancellationToken);
                    }
                    catch (Exception cleanupEx)
                    {
                        _logger.LogWarning(cleanupEx, "Failed to cleanup file {ObjectKey} after transaction failure", uploadResult.ObjectKey);
                    }
                }
            }

            return result;
        }
    }
}
