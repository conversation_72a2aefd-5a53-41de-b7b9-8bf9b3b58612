using HotChocolate.Types;
using HotChocolate;
using Shared.Models.FileStorage;

namespace Shared.GraphQL.Types
{
    public class FileUploadResultType : ObjectType<FileUploadResult>
    {
        protected override void Configure(IObjectTypeDescriptor<FileUploadResult> descriptor)
        {
            descriptor.Field(f => f.FileId).Type<NonNullType<UuidType>>();
            descriptor.Field(f => f.FileName).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.Object<PERSON>ey).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.BucketName).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.SizeBytes).Type<NonNullType<LongType>>();
            descriptor.Field(f => f.ContentType).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.ETag).Type<StringType>();
            descriptor.Field(f => f.AccessUrl).Type<StringType>();
            descriptor.Field(f => f.Version).Type<NonNullType<IntType>>();
            descriptor.Field(f => f.HasThumbnail).Type<NonNullType<BooleanType>>();
            descriptor.Field(f => f.ThumbnailUrl).Type<StringType>();
            descriptor.Field(f => f.CreatedAt).Type<NonNullType<DateTimeType>>();
        }
    }

    public class FileMetadataType : ObjectType<FileMetadata>
    {
        protected override void Configure(IObjectTypeDescriptor<FileMetadata> descriptor)
        {
            descriptor.Field(f => f.Id).Type<NonNullType<UuidType>>();
            descriptor.Field(f => f.FileName).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.ContentType).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.SizeBytes).Type<NonNullType<LongType>>();
            descriptor.Field(f => f.Description).Type<StringType>();
            descriptor.Field(f => f.BucketName).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.ObjectKey).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.CurrentVersion).Type<NonNullType<IntType>>();
            descriptor.Field(f => f.IsLatestVersion).Type<NonNullType<BooleanType>>();
            descriptor.Field(f => f.OriginalFileId).Type<UuidType>();
            descriptor.Field(f => f.AccessUrl).Type<StringType>();
            descriptor.Field(f => f.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(f => f.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(f => f.UpdatedAt).Type<DateTimeType>();
        }
    }

    public class FileStorageTransactionResultType : ObjectType<FileStorageTransactionResult>
    {
        protected override void Configure(IObjectTypeDescriptor<FileStorageTransactionResult> descriptor)
        {
            descriptor.Field(f => f.TransactionId).Type<NonNullType<UuidType>>();
            descriptor.Field(f => f.Success).Type<NonNullType<BooleanType>>();
            descriptor.Field(f => f.UploadResults).Type<NonNullType<ListType<NonNullType<FileUploadResultType>>>>();
            descriptor.Field(f => f.DeletedFileIds).Type<NonNullType<ListType<NonNullType<UuidType>>>>();
            descriptor.Field(f => f.ErrorMessage).Type<StringType>();
        }
    }

    public class ThumbnailInfoType : ObjectType<ThumbnailInfo>
    {
        protected override void Configure(IObjectTypeDescriptor<ThumbnailInfo> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<UuidType>>();
            descriptor.Field(t => t.Width).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Height).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.SizeBytes).Type<NonNullType<LongType>>();
            descriptor.Field(t => t.AccessUrl).Type<StringType>();
        }
    }

    // Input types for mutations - Note: File uploads in GraphQL are handled differently
    // We'll use IFile parameter directly in mutations instead of input types for file uploads

    public class WorkerFileUploadResultType : ObjectType
    {
        protected override void Configure(IObjectTypeDescriptor descriptor)
        {
            descriptor.Name("WorkerFileUploadResult");
            descriptor.Field("workerId").Type<NonNullType<IntType>>();
            descriptor.Field("profilePictureResult").Type<FileUploadResultType>();
            descriptor.Field("documentResults").Type<ListType<NonNullType<FileUploadResultType>>>();
            descriptor.Field("success").Type<NonNullType<BooleanType>>();
            descriptor.Field("errorMessage").Type<StringType>();
        }
    }
}
