using Shared.Models.FileStorage;

namespace Shared.Interfaces
{
    public interface IFileStorageService
    {
        // Basic CRUD Operations
        Task<FileUploadResult> UploadFileAsync(Stream fileStream, FileUploadRequest request, CancellationToken cancellationToken = default);
        Task<FileDownloadResult> DownloadFileAsync(FileDownloadRequest request, CancellationToken cancellationToken = default);
        Task<FileMetadata?> GetFileMetadataAsync(Guid fileId, CancellationToken cancellationToken = default);

        // Simplified Latest Version Operations
        Task<FileUploadResult> UploadLatestFileAsync(Stream fileStream, string fileName, string bucketName, string? description = null, string? folderPath = null, bool generateThumbnail = true, CancellationToken cancellationToken = default);
        Task<FileDownloadResult> DownloadLatestFileAsync(Guid fileId, CancellationToken cancellationToken = default);
        Task<FileMetadata?> GetLatestFileMetadataAsync(Guid fileId, CancellationToken cancellationToken = default);
        Task<bool> DeleteFileAsync(Guid fileId, bool hardDelete = false, CancellationToken cancellationToken = default);
        Task<FileListResult> ListFilesAsync(FileListRequest request, CancellationToken cancellationToken = default);

        // Versioning
        Task<FileUploadResult> CreateFileVersionAsync(Stream fileStream, Guid originalFileId, string? description = null, CancellationToken cancellationToken = default);
        Task<List<FileMetadata>> GetFileVersionsAsync(Guid originalFileId, CancellationToken cancellationToken = default);
        Task<FileDownloadResult> DownloadFileVersionAsync(Guid originalFileId, int version, CancellationToken cancellationToken = default);

        // Thumbnails
        Task<bool> GenerateThumbnailAsync(Guid fileId, CancellationToken cancellationToken = default);
        Task<FileDownloadResult?> DownloadThumbnailAsync(Guid fileId, CancellationToken cancellationToken = default);

        // Bucket Management
        Task<List<BucketInfo>> GetBucketsAsync(CancellationToken cancellationToken = default);
        Task<bool> CreateBucketAsync(string bucketName, CancellationToken cancellationToken = default);
        Task<bool> BucketExistsAsync(string bucketName, CancellationToken cancellationToken = default);

        // Folder Operations
        Task<bool> CreateFolderAsync(string bucketName, string folderPath, CancellationToken cancellationToken = default);
        Task<List<string>> ListFoldersAsync(string bucketName, string? parentPath = null, CancellationToken cancellationToken = default);

        // URL Generation
        Task<string> GeneratePresignedUrlAsync(Guid fileId, TimeSpan expiry, CancellationToken cancellationToken = default);
        Task<string> GeneratePresignedUploadUrlAsync(string bucketName, string objectKey, TimeSpan expiry, CancellationToken cancellationToken = default);

        // Validation
        Task<bool> ValidateFileAsync(Stream fileStream, string fileName, CancellationToken cancellationToken = default);
        bool IsFileTypeAllowed(string contentType);
        bool IsFileSizeAllowed(long sizeBytes);

        // Transaction Support
        Task<FileStorageTransactionResult> ExecuteTransactionAsync(FileStorageTransaction transaction, CancellationToken cancellationToken = default);

        // Utility
        Task<bool> FileExistsAsync(Guid fileId, CancellationToken cancellationToken = default);
        Task<long> GetTotalStorageUsedAsync(string? bucketName = null, CancellationToken cancellationToken = default);
    }
}
