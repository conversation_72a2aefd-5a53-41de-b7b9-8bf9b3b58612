using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Shared.Interfaces;

namespace Shared.Models.FileStorage
{
    public class FileRecord : IAuditableEntity, ISoftDeletable
    {
        [Key]
        public Guid Id { get; set; } = Guid.NewGuid();

        [Required]
        [MaxLength(255)]
        public string FileName { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string ContentType { get; set; } = string.Empty;

        public long SizeBytes { get; set; }

        [MaxLength(500)]
        public string? Description { get; set; }

        [Required]
        [MaxLength(100)]
        public string BucketName { get; set; } = string.Empty;

        [Required]
        [MaxLength(500)]
        public string ObjectKey { get; set; } = string.Empty;

        [MaxLength(100)]
        public string? ETag { get; set; }

        /// <summary>
        /// JSON serialized metadata dictionary
        /// </summary>
        [Column(TypeName = "nvarchar(max)")]
        public string? Metadata { get; set; }

        /// <summary>
        /// Current version number of the file
        /// </summary>
        public int CurrentVersion { get; set; } = 1;

        /// <summary>
        /// Indicates if this is the latest version
        /// </summary>
        public bool IsLatestVersion { get; set; } = true;

        /// <summary>
        /// Reference to the original file if this is a version
        /// </summary>
        public Guid? OriginalFileId { get; set; }

        /// <summary>
        /// URL for accessing the file (can be generated dynamically)
        /// </summary>
        [MaxLength(1000)]
        public string? AccessUrl { get; set; }

        // Navigation Properties
        public virtual FileRecord? OriginalFile { get; set; }
        public virtual ICollection<FileRecord> Versions { get; set; } = new List<FileRecord>();
        public virtual ICollection<FileThumbnail> Thumbnails { get; set; } = new List<FileThumbnail>();

        // IAuditableEntity implementation
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // ISoftDeletable implementation
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }

        // Helper methods
        public bool IsImage()
        {
            return ContentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);
        }

        public bool IsDocument()
        {
            return ContentType.StartsWith("application/") || ContentType.StartsWith("text/");
        }

        public string GetFileExtension()
        {
            return Path.GetExtension(FileName).ToLowerInvariant();
        }

        public string GetSizeFormatted()
        {
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            double len = SizeBytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
