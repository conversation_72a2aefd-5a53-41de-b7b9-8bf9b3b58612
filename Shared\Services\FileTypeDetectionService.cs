using System.Text;

namespace Shared.Services
{
    public static class FileTypeDetectionService
    {
        private static readonly Dictionary<byte[], string> FileSignatures = new()
        {
            // Images
            { new byte[] { 0xFF, 0xD8, 0xFF }, "image/jpeg" },
            { new byte[] { 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A }, "image/png" },
            { new byte[] { 0x47, 0x49, 0x46, 0x38, 0x37, 0x61 }, "image/gif" },
            { new byte[] { 0x47, 0x49, 0x46, 0x38, 0x39, 0x61 }, "image/gif" },
            { new byte[] { 0x42, 0x4D }, "image/bmp" },
            { new byte[] { 0x52, 0x49, 0x46, 0x46 }, "image/webp" }, // RIFF (WebP uses RIFF container)
            { new byte[] { 0x49, 0x49, 0x2A, 0x00 }, "image/tiff" }, // TIFF little endian
            { new byte[] { 0x4D, 0x4D, 0x00, 0x2A }, "image/tiff" }, // TIFF big endian
            
            // Documents
            { new byte[] { 0x25, 0x50, 0x44, 0x46 }, "application/pdf" }, // %PDF
            { new byte[] { 0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1 }, "application/msword" }, // MS Office (legacy)
            { new byte[] { 0x50, 0x4B, 0x03, 0x04 }, "application/zip" }, // ZIP (also used by modern Office docs)
            { new byte[] { 0x50, 0x4B, 0x05, 0x06 }, "application/zip" }, // ZIP (empty archive)
            { new byte[] { 0x50, 0x4B, 0x07, 0x08 }, "application/zip" }, // ZIP (spanned archive)
            
            // Archives
            { new byte[] { 0x1F, 0x8B, 0x08 }, "application/gzip" },
            { new byte[] { 0x42, 0x5A, 0x68 }, "application/x-bzip2" },
            { new byte[] { 0x37, 0x7A, 0xBC, 0xAF, 0x27, 0x1C }, "application/x-7z-compressed" },
        };

        private static readonly Dictionary<string, string> ExtensionMimeTypes = new(StringComparer.OrdinalIgnoreCase)
        {
            // Images
            { ".jpg", "image/jpeg" },
            { ".jpeg", "image/jpeg" },
            { ".png", "image/png" },
            { ".gif", "image/gif" },
            { ".bmp", "image/bmp" },
            { ".webp", "image/webp" },
            { ".svg", "image/svg+xml" },
            { ".tiff", "image/tiff" },
            { ".tif", "image/tiff" },
            { ".ico", "image/x-icon" },
            
            // Documents
            { ".pdf", "application/pdf" },
            { ".doc", "application/msword" },
            { ".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document" },
            { ".xls", "application/vnd.ms-excel" },
            { ".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" },
            { ".ppt", "application/vnd.ms-powerpoint" },
            { ".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation" },
            { ".txt", "text/plain" },
            { ".csv", "text/csv" },
            { ".rtf", "application/rtf" },
            { ".odt", "application/vnd.oasis.opendocument.text" },
            { ".ods", "application/vnd.oasis.opendocument.spreadsheet" },
            { ".odp", "application/vnd.oasis.opendocument.presentation" },
            
            // Archives
            { ".zip", "application/zip" },
            { ".rar", "application/x-rar-compressed" },
            { ".7z", "application/x-7z-compressed" },
            { ".tar", "application/x-tar" },
            { ".gz", "application/gzip" },
            { ".bz2", "application/x-bzip2" },
        };

        /// <summary>
        /// Detects the MIME type of a file from its stream content
        /// </summary>
        /// <param name="stream">The file stream</param>
        /// <param name="fileName">Optional filename for fallback detection</param>
        /// <returns>The detected MIME type</returns>
        public static async Task<string> DetectMimeTypeAsync(Stream stream, string? fileName = null)
        {
            if (stream == null || !stream.CanRead)
                throw new ArgumentException("Stream must be readable", nameof(stream));

            var originalPosition = stream.Position;
            try
            {
                stream.Position = 0;
                
                // Read first 16 bytes for signature detection
                var buffer = new byte[16];
                var bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                
                if (bytesRead > 0)
                {
                    // Try to detect by file signature
                    var detectedType = DetectBySignature(buffer, bytesRead);
                    if (!string.IsNullOrEmpty(detectedType))
                    {
                        // Special handling for ZIP-based formats (Office documents)
                        if (detectedType == "application/zip" && !string.IsNullOrEmpty(fileName))
                        {
                            var officeType = DetectOfficeDocumentType(fileName);
                            if (!string.IsNullOrEmpty(officeType))
                                return officeType;
                        }
                        
                        return detectedType;
                    }
                }

                // Fallback to extension-based detection
                if (!string.IsNullOrEmpty(fileName))
                {
                    var extension = Path.GetExtension(fileName);
                    if (ExtensionMimeTypes.TryGetValue(extension, out var mimeType))
                        return mimeType;
                }

                // Default fallback
                return "application/octet-stream";
            }
            finally
            {
                stream.Position = originalPosition;
            }
        }

        /// <summary>
        /// Detects MIME type synchronously from a byte array
        /// </summary>
        /// <param name="fileBytes">The file bytes</param>
        /// <param name="fileName">Optional filename for fallback detection</param>
        /// <returns>The detected MIME type</returns>
        public static string DetectMimeType(byte[] fileBytes, string? fileName = null)
        {
            if (fileBytes == null || fileBytes.Length == 0)
                throw new ArgumentException("File bytes cannot be null or empty", nameof(fileBytes));

            // Try to detect by file signature
            var detectedType = DetectBySignature(fileBytes, fileBytes.Length);
            if (!string.IsNullOrEmpty(detectedType))
            {
                // Special handling for ZIP-based formats (Office documents)
                if (detectedType == "application/zip" && !string.IsNullOrEmpty(fileName))
                {
                    var officeType = DetectOfficeDocumentType(fileName);
                    if (!string.IsNullOrEmpty(officeType))
                        return officeType;
                }
                
                return detectedType;
            }

            // Fallback to extension-based detection
            if (!string.IsNullOrEmpty(fileName))
            {
                var extension = Path.GetExtension(fileName);
                if (ExtensionMimeTypes.TryGetValue(extension, out var mimeType))
                    return mimeType;
            }

            return "application/octet-stream";
        }

        /// <summary>
        /// Validates if a file type is allowed based on configuration
        /// </summary>
        /// <param name="contentType">The content type to validate</param>
        /// <param name="allowedTypes">Array of allowed content types</param>
        /// <returns>True if the content type is allowed</returns>
        public static bool IsFileTypeAllowed(string contentType, string[] allowedTypes)
        {
            if (string.IsNullOrEmpty(contentType) || allowedTypes == null || allowedTypes.Length == 0)
                return false;

            return allowedTypes.Contains(contentType, StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Checks if a file is an image based on its content type
        /// </summary>
        /// <param name="contentType">The content type</param>
        /// <returns>True if the file is an image</returns>
        public static bool IsImage(string contentType)
        {
            return !string.IsNullOrEmpty(contentType) && 
                   contentType.StartsWith("image/", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Checks if a file is a document based on its content type
        /// </summary>
        /// <param name="contentType">The content type</param>
        /// <returns>True if the file is a document</returns>
        public static bool IsDocument(string contentType)
        {
            return !string.IsNullOrEmpty(contentType) && 
                   (contentType.StartsWith("application/", StringComparison.OrdinalIgnoreCase) ||
                    contentType.StartsWith("text/", StringComparison.OrdinalIgnoreCase));
        }

        private static string? DetectBySignature(byte[] buffer, int bytesRead)
        {
            foreach (var signature in FileSignatures)
            {
                if (bytesRead >= signature.Key.Length && 
                    buffer.Take(signature.Key.Length).SequenceEqual(signature.Key))
                {
                    return signature.Value;
                }
            }
            return null;
        }

        private static string? DetectOfficeDocumentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".xlsx" => "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                ".pptx" => "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                _ => null
            };
        }
    }
}
