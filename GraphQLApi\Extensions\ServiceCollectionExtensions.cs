using Microsoft.EntityFrameworkCore;
using GraphQLApi.Data;
using GraphQLApi.Services;
using GraphQLApi.GraphQL.Queries;
using GraphQLApi.GraphQL.Mutations;
using Shared.GraphQL.Models;
using Shared.GraphQL.Types;
using HotChocolate.Data;
using Minio;
using Shared.Configuration;
using Shared.Interfaces;
using Shared.Services;

namespace GraphQLApi.Extensions
{
    public static class ServiceCollectionExtensions
    {

        public static IServiceCollection AddApplicationServices(this IServiceCollection services)
        {
            services.AddScoped<IWorkerService, WorkerService>();
            services.AddScoped<ITrainingService, TrainingService>();
            services.AddScoped<ITradeService, TradeService>();
            services.AddScoped<ISkillService, SkillService>();
            services.AddScoped<IPhotoService, PhotoService>();
            services.AddScoped<IWorkerAttendanceService, WorkerAttendanceService>();
            services.AddScoped<ITrainingStatusService, TrainingStatusService>();
            services.AddScoped<ITaskService, TaskService>();
            services.AddScoped<IEquipmentService, EquipmentService>();
            services.AddScoped<IRelationshipService, RelationshipService>();
            services.AddHostedService<TrainingStatusBackgroundService>();
            return services;
        }

        public static IServiceCollection AddHikvisionServices(this IServiceCollection services, IConfiguration configuration)
        {
            var baseUrl = configuration["HikvisionApi:BaseUrl"]
                ?? throw new ArgumentNullException("HikvisionApi:BaseUrl is missing in configuration");

            services.AddHttpClient<IHikvisionService, HikvisionService>(client =>
            {
                client.BaseAddress = new Uri(baseUrl);
            });
            services.AddScoped<IHikvisionService, HikvisionService>();

            return services;
        }

        public static IServiceCollection AddGraphQLServices(this IServiceCollection services)
        {
            services
                .AddGraphQLServer()
                .ModifyRequestOptions(opt => opt.IncludeExceptionDetails = true)
                .AddQueryType<Query>()
                .AddMutationType<Mutation>()
                .AddProjections()
                .AddFiltering()
                .AddType<WorkerType>()
                .AddType<TrainingType>()
                .AddType<TradeType>()
                .AddType<SkillType>()
                .AddType<TaskType>()
                .AddType<EquipmentType>()
                .AddType<WorkerAttendanceType>()
                .AddType<ToolboxSessionType>()
                .AddType<WorkerTrainingHistoryType>()
                .AddType<IncidentType>()
                .AddType<EnumType<Shared.Enums.TrainingStatus>>()
                .AddType<EnumType<Shared.Enums.TaskStatus>>()
                .AddType<EnumType<Shared.Enums.TaskPriority>>()
                .AddType<EnumType<Shared.Enums.InspectionStatus>>()
                .AddType<EnumType<Shared.Enums.IncidentStatus>>();

            return services;
        }

        public static IServiceCollection AddFileStorageServices(this IServiceCollection services, IConfiguration configuration)
        {
            // Configure MinIO settings
            var minioConfig = configuration.GetSection("FileStorage:MinIO");
            var minioConfiguration = new MinIOConfiguration();
            minioConfig.Bind(minioConfiguration);

            // Configure file storage settings
            var fileStorageConfig = configuration.GetSection("FileStorage");
            services.Configure<FileStorageConfiguration>(fileStorageConfig);

            // Register MinIO client
            services.AddSingleton<IMinioClient>(provider =>
            {
                var client = new MinioClient()
                    .WithEndpoint(minioConfiguration.Endpoint)
                    .WithCredentials(minioConfiguration.AccessKey, minioConfiguration.SecretKey)
                    .WithRegion(minioConfiguration.Region);

                if (minioConfiguration.UseSSL)
                {
                    client = client.WithSSL();
                }

                return client.Build();
            });

            // Register file storage services
            services.AddScoped<IFileStorageService, FileStorageService>();
            services.AddScoped<IThumbnailService, ThumbnailService>();
            services.AddScoped<IFileStorageTransactionService, FileStorageTransactionService>();

            return services;
        }
    }
}