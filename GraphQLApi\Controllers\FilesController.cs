using Microsoft.AspNetCore.Mvc;
using Shared.Exceptions;
using Shared.Interfaces;
using Shared.Models.FileStorage;
using System.Net;
using FileNotFoundException = Shared.Exceptions.FileNotFoundException;

namespace GraphQLApi.Controllers
{
    [ApiController]
    [Route("files/[controller]")]
    public class FilesController : ControllerBase
    {
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<FilesController> _logger;

        public FilesController(IFileStorageService fileStorageService, ILogger<FilesController> logger)
        {
            _fileStorageService = fileStorageService;
            _logger = logger;
        }

        /// <summary>
        /// Download a file by its ID
        /// </summary>
        /// <param name="fileId">The file ID</param>
        /// <param name="version">Optional version number</param>
        /// <param name="inline">Whether to display inline or as attachment</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The file content</returns>
        [HttpGet("{fileId:guid}")]
        public async Task<IActionResult> DownloadFile(
            Guid fileId, 
            [FromQuery] int? version = null, 
            [FromQuery] bool inline = false,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var request = new FileDownloadRequest
                {
                    FileId = fileId,
                    Version = version
                };

                var result = await _fileStorageService.DownloadFileAsync(request, cancellationToken);

                var contentDisposition = inline ? "inline" : "attachment";
                Response.Headers.Add("Content-Disposition", $"{contentDisposition}; filename=\"{result.FileName}\"");
                
                if (!string.IsNullOrEmpty(result.ETag))
                {
                    Response.Headers.Add("ETag", result.ETag);
                }

                return File(result.FileStream, result.ContentType, result.FileName);
            }
            catch (FileNotFoundException)
            {
                return NotFound($"File with ID {fileId} not found");
            }
            catch (FileStorageException ex)
            {
                _logger.LogError(ex, "Error downloading file {FileId}", fileId);
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error downloading file");
            }
        }

        /// <summary>
        /// Download a thumbnail for a file
        /// </summary>
        /// <param name="fileId">The file ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>The thumbnail content</returns>
        [HttpGet("{fileId:guid}/thumbnail")]
        public async Task<IActionResult> DownloadThumbnail(Guid fileId, CancellationToken cancellationToken = default)
        {
            try
            {
                var result = await _fileStorageService.DownloadThumbnailAsync(fileId, cancellationToken);
                
                if (result == null)
                {
                    return NotFound($"Thumbnail for file {fileId} not found");
                }

                Response.Headers.Add("Content-Disposition", $"inline; filename=\"{result.FileName}\"");
                
                if (!string.IsNullOrEmpty(result.ETag))
                {
                    Response.Headers.Add("ETag", result.ETag);
                }

                return File(result.FileStream, result.ContentType, result.FileName);
            }
            catch (FileStorageException ex)
            {
                _logger.LogError(ex, "Error downloading thumbnail for file {FileId}", fileId);
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error downloading thumbnail");
            }
        }

        /// <summary>
        /// Get file metadata
        /// </summary>
        /// <param name="fileId">The file ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>File metadata</returns>
        [HttpGet("{fileId:guid}/metadata")]
        public async Task<IActionResult> GetFileMetadata(Guid fileId, CancellationToken cancellationToken = default)
        {
            try
            {
                var metadata = await _fileStorageService.GetFileMetadataAsync(fileId, cancellationToken);
                
                if (metadata == null)
                {
                    return NotFound($"File with ID {fileId} not found");
                }

                return Ok(metadata);
            }
            catch (FileStorageException ex)
            {
                _logger.LogError(ex, "Error getting metadata for file {FileId}", fileId);
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error getting file metadata");
            }
        }

        /// <summary>
        /// Upload a file
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="bucketName">The bucket name</param>
        /// <param name="description">Optional description</param>
        /// <param name="folderPath">Optional folder path</param>
        /// <param name="generateThumbnail">Whether to generate thumbnail</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Upload result</returns>
        [HttpPost("upload")]
        public async Task<IActionResult> UploadFile(
            IFormFile file,
            [FromForm] string bucketName,
            [FromForm] string? description = null,
            [FromForm] string? folderPath = null,
            [FromForm] bool generateThumbnail = true,
            CancellationToken cancellationToken = default)
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest("No file provided");
            }

            try
            {
                var request = new FileUploadRequest
                {
                    FileName = file.FileName,
                    BucketName = bucketName,
                    Description = description,
                    FolderPath = folderPath,
                    GenerateThumbnail = generateThumbnail
                };

                using var stream = file.OpenReadStream();
                var result = await _fileStorageService.UploadFileAsync(stream, request, cancellationToken);

                return Ok(result);
            }
            catch (InvalidFileTypeException ex)
            {
                return BadRequest($"Invalid file type: {ex.Message}");
            }
            catch (FileSizeExceededException ex)
            {
                return BadRequest($"File size exceeds limit: {ex.Message}");
            }
            catch (FileStorageException ex)
            {
                _logger.LogError(ex, "Error uploading file {FileName}", file.FileName);
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error uploading file");
            }
        }

        /// <summary>
        /// Delete a file
        /// </summary>
        /// <param name="fileId">The file ID</param>
        /// <param name="hardDelete">Whether to perform hard delete</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Success status</returns>
        [HttpDelete("{fileId:guid}")]
        public async Task<IActionResult> DeleteFile(
            Guid fileId, 
            [FromQuery] bool hardDelete = false,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var result = await _fileStorageService.DeleteFileAsync(fileId, hardDelete, cancellationToken);
                
                if (!result)
                {
                    return NotFound($"File with ID {fileId} not found");
                }

                return Ok(new { success = true, message = "File deleted successfully" });
            }
            catch (FileStorageException ex)
            {
                _logger.LogError(ex, "Error deleting file {FileId}", fileId);
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error deleting file");
            }
        }

        /// <summary>
        /// List files with filtering and pagination
        /// </summary>
        /// <param name="request">List request parameters</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Paginated file list</returns>
        [HttpGet]
        public async Task<IActionResult> ListFiles([FromQuery] FileListRequest request, CancellationToken cancellationToken = default)
        {
            try
            {
                var result = await _fileStorageService.ListFilesAsync(request, cancellationToken);
                return Ok(result);
            }
            catch (FileStorageException ex)
            {
                _logger.LogError(ex, "Error listing files");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error listing files");
            }
        }

        /// <summary>
        /// Get file versions
        /// </summary>
        /// <param name="fileId">The original file ID</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>List of file versions</returns>
        [HttpGet("{fileId:guid}/versions")]
        public async Task<IActionResult> GetFileVersions(Guid fileId, CancellationToken cancellationToken = default)
        {
            try
            {
                var versions = await _fileStorageService.GetFileVersionsAsync(fileId, cancellationToken);
                return Ok(versions);
            }
            catch (FileStorageException ex)
            {
                _logger.LogError(ex, "Error getting versions for file {FileId}", fileId);
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error getting file versions");
            }
        }

        /// <summary>
        /// Generate a presigned URL for file access
        /// </summary>
        /// <param name="fileId">The file ID</param>
        /// <param name="expiryHours">Expiry time in hours (default: 1)</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Presigned URL</returns>
        [HttpGet("{fileId:guid}/presigned-url")]
        public async Task<IActionResult> GeneratePresignedUrl(
            Guid fileId, 
            [FromQuery] int expiryHours = 1,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var expiry = TimeSpan.FromHours(Math.Max(1, Math.Min(24, expiryHours))); // Limit between 1-24 hours
                var url = await _fileStorageService.GeneratePresignedUrlAsync(fileId, expiry, cancellationToken);
                
                return Ok(new { url, expiresAt = DateTime.UtcNow.Add(expiry) });
            }
            catch (FileNotFoundException)
            {
                return NotFound($"File with ID {fileId} not found");
            }
            catch (FileStorageException ex)
            {
                _logger.LogError(ex, "Error generating presigned URL for file {FileId}", fileId);
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error generating presigned URL");
            }
        }

        /// <summary>
        /// Get storage statistics
        /// </summary>
        /// <param name="bucketName">Optional bucket name filter</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>Storage statistics</returns>
        [HttpGet("stats")]
        public async Task<IActionResult> GetStorageStats(
            [FromQuery] string? bucketName = null,
            CancellationToken cancellationToken = default)
        {
            try
            {
                var totalStorage = await _fileStorageService.GetTotalStorageUsedAsync(bucketName, cancellationToken);
                var buckets = await _fileStorageService.GetBucketsAsync(cancellationToken);
                
                return Ok(new 
                { 
                    totalStorageBytes = totalStorage,
                    totalStorageFormatted = FormatBytes(totalStorage),
                    buckets = buckets.Where(b => string.IsNullOrEmpty(bucketName) || b.Name == bucketName)
                });
            }
            catch (FileStorageException ex)
            {
                _logger.LogError(ex, "Error getting storage stats");
                return StatusCode((int)HttpStatusCode.InternalServerError, "Error getting storage statistics");
            }
        }

        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB", "TB" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
