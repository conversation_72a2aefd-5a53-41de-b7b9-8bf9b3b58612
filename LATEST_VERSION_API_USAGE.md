# Latest Version File API Usage

This document shows how to use the simplified file storage API endpoints that work with the latest version of files without requiring version numbers.

## New Simplified Endpoints

### 1. Upload Latest File
**Endpoint:** `POST /files/upload-latest`

This endpoint uploads a file as the latest version. If the file already exists, it creates a new version automatically.

**Example:**
```bash
curl -X POST "https://localhost:7000/files/upload-latest" \
  -F "file=@document.pdf" \
  -F "bucketName=documents" \
  -F "description=Updated document" \
  -F "folderPath=contracts/2024" \
  -F "generateThumbnail=true"
```

**Response:**
```json
{
  "fileId": "123e4567-e89b-12d3-a456-426614174000",
  "fileName": "document.pdf",
  "objectKey": "documents/contracts/2024/document.pdf",
  "bucketName": "documents",
  "sizeBytes": 1048576,
  "contentType": "application/pdf",
  "eTag": "d41d8cd98f00b204e9800998ecf8427e",
  "accessUrl": "https://localhost:9000/documents/contracts/2024/document.pdf?...",
  "version": 1,
  "hasThumbnail": true,
  "thumbnailUrl": "https://localhost:9000/thumbnails/...",
  "createdAt": "2024-01-15T10:30:00Z"
}
```

### 2. Download Latest File
**Endpoint:** `GET /files/{fileId}/latest`

This endpoint downloads the latest version of a file without needing to specify a version number.

**Example:**
```bash
# Download as attachment
curl -X GET "https://localhost:7000/files/123e4567-e89b-12d3-a456-426614174000/latest" \
  -o downloaded_file.pdf

# Download inline (for viewing in browser)
curl -X GET "https://localhost:7000/files/123e4567-e89b-12d3-a456-426614174000/latest?inline=true"
```

### 3. Get Latest File Metadata
**Endpoint:** `GET /files/{fileId}/latest/metadata`

This endpoint returns metadata for the latest version of a file.

**Example:**
```bash
curl -X GET "https://localhost:7000/files/123e4567-e89b-12d3-a456-426614174000/latest/metadata" \
  -H "Accept: application/json"
```

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "fileName": "document.pdf",
  "contentType": "application/pdf",
  "sizeBytes": 1048576,
  "description": "Updated document",
  "bucketName": "documents",
  "objectKey": "documents/contracts/2024/document.pdf",
  "currentVersion": 3,
  "isLatestVersion": true,
  "originalFileId": null,
  "accessUrl": "https://localhost:9000/documents/contracts/2024/document.pdf?...",
  "metadata": {},
  "thumbnails": [
    {
      "thumbnailFileId": "456e7890-e89b-12d3-a456-426614174001",
      "thumbnailUrl": "https://localhost:9000/thumbnails/...",
      "width": 200,
      "height": 300
    }
  ],
  "createdAt": "2024-01-15T10:30:00Z",
  "createdBy": "<EMAIL>",
  "updatedAt": "2024-01-15T11:45:00Z",
  "updatedBy": "<EMAIL>"
}
```

## C# Service Usage

You can also use the simplified methods directly in your C# code:

```csharp
public class DocumentController : ControllerBase
{
    private readonly IFileStorageService _fileStorageService;

    public DocumentController(IFileStorageService fileStorageService)
    {
        _fileStorageService = fileStorageService;
    }

    [HttpPost("upload")]
    public async Task<IActionResult> UploadDocument(IFormFile file)
    {
        using var stream = file.OpenReadStream();
        
        // Upload as latest version - simple!
        var result = await _fileStorageService.UploadLatestFileAsync(
            stream,
            file.FileName,
            "documents",
            "User uploaded document",
            "user-uploads",
            generateThumbnail: true
        );

        return Ok(result);
    }

    [HttpGet("{fileId}/download")]
    public async Task<IActionResult> DownloadDocument(Guid fileId)
    {
        // Download latest version - simple!
        var result = await _fileStorageService.DownloadLatestFileAsync(fileId);
        
        return File(result.FileStream, result.ContentType, result.FileName);
    }

    [HttpGet("{fileId}/info")]
    public async Task<IActionResult> GetDocumentInfo(Guid fileId)
    {
        // Get latest metadata - simple!
        var metadata = await _fileStorageService.GetLatestFileMetadataAsync(fileId);
        
        if (metadata == null)
            return NotFound();
            
        return Ok(metadata);
    }
}
```

## Comparison with Existing Endpoints

### Before (with version handling):
```bash
# Upload (always creates new version)
POST /files/upload

# Download specific version
GET /files/{fileId}?version=3

# Download latest (version parameter optional)
GET /files/{fileId}

# Get metadata
GET /files/{fileId}/metadata
```

### After (simplified latest version):
```bash
# Upload latest (clearer intent)
POST /files/upload-latest

# Download latest (explicit)
GET /files/{fileId}/latest

# Get latest metadata (explicit)
GET /files/{fileId}/latest/metadata
```

## Benefits

1. **Clearer Intent**: The endpoint names make it obvious you're working with the latest version
2. **Simplified Usage**: No need to track or specify version numbers for common operations
3. **Backward Compatible**: Original endpoints still work for advanced version management
4. **Consistent API**: All "latest" operations follow the same URL pattern

## When to Use Each Approach

**Use the simplified latest version endpoints when:**
- You just want to upload/download the current version of a file
- You don't need to manage multiple versions explicitly
- You're building simple file upload/download functionality

**Use the original versioned endpoints when:**
- You need to access specific historical versions
- You're building version management features
- You need fine-grained control over file versioning

Both approaches work together seamlessly in the same application!
