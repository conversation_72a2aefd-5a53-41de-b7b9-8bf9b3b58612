﻿using GraphQLApi.Data;
using GraphQLApi.Services;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using HotChocolate;
using Shared.Utils;
using Shared.Enums;
using Shared.Interfaces;
using Shared.Models.FileStorage;
using Shared.Constants;
using Task = Shared.GraphQL.Models.Task;

namespace GraphQLApi.GraphQL.Mutations
{
    public class Mutation
    {
        private readonly IWorkerService _workerService;
        private readonly ITrainingService _trainingService;
        private readonly ITradeService _tradeService;
        private readonly ISkillService _skillService;
        private readonly ITrainingStatusService _trainingStatusService;
        private readonly ITaskService _taskService;
        private readonly IEquipmentService _equipmentService;
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly IRelationshipService _relationshipService;
        private readonly IFileStorageService _fileStorageService;
        private readonly IFileStorageTransactionService _fileStorageTransactionService;
        private readonly ILogger<Mutation> _logger;

        public Mutation(
            IWorkerService workerService,
            ITrainingService trainingService,
            ITradeService tradeService,
            ISkillService skillService,
            ITrainingStatusService trainingStatusService,
            ITaskService taskService,
            IEquipmentService equipmentService,
            IDbContextFactory<AppDbContext> contextFactory,
            IRelationshipService relationshipService,
            IFileStorageService fileStorageService,
            IFileStorageTransactionService fileStorageTransactionService,
            ILogger<Mutation> logger)
        {
            _workerService = workerService;
            _trainingService = trainingService;
            _tradeService = tradeService;
            _skillService = skillService;
            _trainingStatusService = trainingStatusService;
            _taskService = taskService;
            _equipmentService = equipmentService;
            _contextFactory = contextFactory;
            _relationshipService = relationshipService;
            _fileStorageService = fileStorageService;
            _fileStorageTransactionService = fileStorageTransactionService;
            _logger = logger;
        }

        // Worker Mutations
        public async Task<Worker> CreateWorker(
            string name,
            string company,
            string nationalId,
            string gender,
            string phoneNumber,
            DateOnly? dateOfBirth = null,
            List<int>? trainingIds = null,
            List<int>? tradeIds = null,
            List<int>? skillIds = null,
            string? mpesaNumber = null,
            string? email = null,
            DateTime? inductionDate = null,
            DateTime? medicalCheckDate = null)
        {
            trainingIds ??= new List<int>();
            tradeIds ??= new List<int>();
            skillIds ??= new List<int>();

            try
            {
                var worker = new Worker
                {
                    Name = name,
                    Company = company,
                    NationalId = nationalId,
                    Gender = gender,
                    DateOfBirth = dateOfBirth,
                    ManHours = 0,
                    Rating = 0,
                    PhoneNumber = phoneNumber,
                    MpesaNumber = mpesaNumber,
                    Email = email,
                    InductionDate = inductionDate,
                    MedicalCheckDate = medicalCheckDate
                };

                // Create worker first
                var createdWorker = await _workerService.CreateWorkerAsync(worker);

                // Add relationships if specified using the relationship service
                if (trainingIds.Any())
                {
                    await _relationshipService.AssignTrainingsToWorkerAsync(createdWorker.Id, trainingIds);
                }
                if (tradeIds.Any())
                {
                    await _relationshipService.AssignTradesToWorkerAsync(createdWorker.Id, tradeIds);
                }
                if (skillIds.Any())
                {
                    await _relationshipService.AssignSkillsToWorkerAsync(createdWorker.Id, skillIds);
                }

                return await _workerService.GetWorkerByIdAsync(createdWorker.Id) ?? createdWorker;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Worker?> UpdateWorker(
            int id,
            string? name = null,
            string? company = null,
            DateOnly? dateOfBirth = null,
            List<int>? trainingIds = null,
            List<int>? tradeIds = null,
            List<int>? skillIds = null,
            int? manHours = null,
            double? rating = null,
            string? gender = null,
            string? phoneNumber = null,
            string? mpesaNumber = null,
            string? email = null,
            DateTime? inductionDate = null,
            DateTime? medicalCheckDate = null)
        {
            var existingWorker = await _workerService.GetWorkerByIdAsync(id);
            if (existingWorker == null)
                return null;

            var updatedWorker = new Worker
            {
                Id = existingWorker.Id,
                Name = name ?? existingWorker.Name,
                Company = company ?? existingWorker.Company,
                DateOfBirth = dateOfBirth ?? existingWorker.DateOfBirth,
                ManHours = manHours ?? existingWorker.ManHours,
                Rating = rating ?? existingWorker.Rating,
                Gender = gender ?? existingWorker.Gender,
                NationalId = existingWorker.NationalId,
                PhoneNumber = phoneNumber ?? existingWorker.PhoneNumber,
                MpesaNumber = mpesaNumber ?? existingWorker.MpesaNumber,
                Email = email ?? existingWorker.Email,
                InductionDate = inductionDate ?? existingWorker.InductionDate,
                MedicalCheckDate = medicalCheckDate ?? existingWorker.MedicalCheckDate
            };

            var result = await _workerService.UpdateWorkerAsync(id, updatedWorker);

            // Update relationships if specified using the relationship service
            if (trainingIds != null || tradeIds != null || skillIds != null)
            {
                await _relationshipService.UpdateWorkerRelationshipsAsync(id, trainingIds, tradeIds, skillIds);
            }

            var finalWorker = await _workerService.GetWorkerByIdAsync(id);
            if (finalWorker == null)
            {
                throw new InvalidOperationException($"Worker with id {id} was not found after update. It may have been deleted during the update process.");
            }
            return finalWorker;
        }

        public async Task<bool> DeleteWorker(int id)
        {
            return await _workerService.DeleteWorkerAsync(id);
        }

        // Training Mutations
        public async Task<Training> CreateTraining(
            string name,
            string? description = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            string? duration = null,
            int? validityPeriodMonths = null,
            string? trainingType = null,
            string? trainer = null,
            string? frequency = null,
            TrainingStatus status = TrainingStatus.Scheduled,
            List<int>? workerIds = null)
        {
            workerIds ??= new List<int>();

            try
            {
                // Validate duration format if provided
                if (!string.IsNullOrEmpty(duration))
                {
                    try
                    {
                        TrainingDuration.Parse(duration);
                    }
                    catch (FormatException ex)
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            ex.Message)
                        );
                    }
                }

                var training = new Training
                {
                    Name = name,
                    Description = description,
                    StartDate = startDate,
                    EndDate = endDate,
                    Duration = duration,
                    ValidityPeriodMonths = validityPeriodMonths,
                    TrainingType = trainingType,
                    Trainer = trainer,
                    Frequency = frequency,
                    Status = status
                };

                var createdTraining = await _trainingService.CreateTrainingAsync(training);

                // Add workers if specified using the relationship service
                if (workerIds.Any())
                {
                    await _relationshipService.AssignWorkersToTrainingAsync(createdTraining.Id, workerIds);
                }

                return await _trainingService.GetTrainingByIdAsync(createdTraining.Id) ?? createdTraining;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Training?> UpdateTraining(
            int id,
            string? name = null,
            string? description = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            string? duration = null,
            int? validityPeriodMonths = null,
            string? trainingType = null,
            string? trainer = null,
            string? frequency = null,
            TrainingStatus? status = null,
            List<int>? workerIds = null)
        {
            var existingTraining = await _trainingService.GetTrainingByIdAsync(id);
            if (existingTraining == null)
            {
                throw new GraphQLException(new Error(
                    "NotFound",
                    $"Training with ID {id} not found.")
                );
            }

            // Validate duration format if provided
            if (duration != null)
            {
                if (string.IsNullOrEmpty(duration))
                {
                    duration = null;
                }
                else
                {
                    try
                    {
                        TrainingDuration.Parse(duration);
                    }
                    catch (FormatException ex)
                    {
                        throw new GraphQLException(new Error(
                            "Validation",
                            ex.Message)
                        );
                    }
                }
            }

            var updatedTraining = new Training
            {
                Id = existingTraining.Id,
                Name = name ?? existingTraining.Name,
                Description = description ?? existingTraining.Description,
                StartDate = startDate ?? existingTraining.StartDate,
                EndDate = endDate ?? existingTraining.EndDate,
                Duration = duration ?? existingTraining.Duration,
                ValidityPeriodMonths = validityPeriodMonths ?? existingTraining.ValidityPeriodMonths,
                TrainingType = trainingType ?? existingTraining.TrainingType,
                Trainer = trainer ?? existingTraining.Trainer,
                Frequency = frequency ?? existingTraining.Frequency,
                Status = status ?? existingTraining.Status
            };

            var result = await _trainingService.UpdateTrainingAsync(id, updatedTraining);

            // Update workers if specified using the relationship service
            if (workerIds != null)
            {
                await _relationshipService.UpdateTrainingRelationshipsAsync(id, workerIds);
            }

            return await _trainingService.GetTrainingByIdAsync(id);
        }

        public async Task<bool> DeleteTraining(int id)
        {
            return await _trainingService.DeleteTrainingAsync(id);
        }

        public async Task<int> AssignTrainingToWorkersByTrade(
            int trainingId,
            List<int> tradeIds)
        {
            try
            {
                await using var context = await _contextFactory.CreateDbContextAsync();

                // Get the training
                var training = await context.Trainings
                    .Include(t => t.Workers)
                    .FirstOrDefaultAsync(t => t.Id == trainingId);

                if (training == null)
                {
                    throw new GraphQLException(new Error(
                        "NotFound",
                        $"Training with ID {trainingId} not found.")
                    );
                }

                // Get all workers with the specified trades
                var workersToAssign = await context.Workers
                    .Include(w => w.Trades)
                    .Where(w => w.Trades.Any(t => tradeIds.Contains(t.Id)))
                    .ToListAsync();

                int assignedCount = 0;
                foreach (var worker in workersToAssign)
                {
                    // Only add if not already assigned
                    if (!training.Workers.Contains(worker))
                    {
                        training.Workers.Add(worker);
                        assignedCount++;
                    }
                }

                await context.SaveChangesAsync();
                return assignedCount;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<WorkerTrainingHistory> CompleteTraining(
            int workerId,
            int trainingId,
            DateTime? completionDate = null,
            decimal? score = null,
            string? notes = null)
        {
            var actualCompletionDate = completionDate ?? DateTime.UtcNow;
            return await _trainingStatusService.CompleteTrainingAsync(workerId, trainingId, actualCompletionDate, score, notes);
        }

        public async Task<bool> UpdateTrainingStatuses()
        {
            try
            {
                await _trainingStatusService.UpdateTrainingStatusesAsync();
                await _trainingStatusService.UpdateTrainingHistoryStatusesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while updating training statuses.");
                return false;
            }
        }

        // Trade Mutations
        public async Task<Trade> CreateTrade(
            string name,
            string? description = null,
            List<int>? workerIds = null)
        {
            workerIds ??= new List<int>();

            try
            {
                var trade = new Trade
                {
                    Name = name,
                    Description = description
                };

                var createdTrade = await _tradeService.CreateTradeAsync(trade);

                // Add workers if specified
                if (workerIds.Any())
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var tradeEntity = await context.Trades
                        .Include(t => t.Workers)
                        .FirstOrDefaultAsync(t => t.Id == createdTrade.Id);

                    if (tradeEntity != null)
                    {
                        foreach (var workerId in workerIds)
                        {
                            var worker = await context.Workers.FindAsync(workerId);
                            if (worker != null)
                            {
                                tradeEntity.Workers.Add(worker);
                            }
                        }
                        await context.SaveChangesAsync();
                    }
                }

                return await _tradeService.GetTradeByIdAsync(createdTrade.Id) ?? createdTrade;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Trade?> UpdateTrade(
            int id,
            string? name = null,
            string? description = null,
            List<int>? workerIds = null)
        {
            var existingTrade = await _tradeService.GetTradeByIdAsync(id);
            if (existingTrade == null)
                return null;

            var updatedTrade = new Trade
            {
                Id = existingTrade.Id,
                Name = name ?? existingTrade.Name,
                Description = description ?? existingTrade.Description
            };

            var result = await _tradeService.UpdateTradeAsync(id, updatedTrade, "System");

            // Update workers if specified
            if (workerIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var tradeEntity = await context.Trades
                    .Include(t => t.Workers)
                    .FirstOrDefaultAsync(t => t.Id == id);

                if (tradeEntity != null)
                {
                    // Clear existing workers
                    tradeEntity.Workers.Clear();

                    // Add new workers
                    foreach (var workerId in workerIds)
                    {
                        var worker = await context.Workers.FindAsync(workerId);
                        if (worker != null)
                        {
                            tradeEntity.Workers.Add(worker);
                        }
                    }
                    await context.SaveChangesAsync();
                }
            }

            return await _tradeService.GetTradeByIdAsync(id);
        }

        public async Task<bool> DeleteTrade(int id)
        {
            return await _tradeService.DeleteTradeAsync(id);
        }

        // Skill Mutations
        public async Task<Skill> CreateSkill(
            string name,
            string? description = null,
            List<int>? workerIds = null)
        {
            workerIds ??= new List<int>();

            try
            {
                var skill = new Skill
                {
                    Name = name,
                    Description = description
                };

                var createdSkill = await _skillService.CreateSkillAsync(skill);

                // Add workers if specified
                if (workerIds.Any())
                {
                    await using var context = await _contextFactory.CreateDbContextAsync();
                    var skillEntity = await context.Skills
                        .Include(s => s.Workers)
                        .FirstOrDefaultAsync(s => s.Id == createdSkill.Id);

                    if (skillEntity != null)
                    {
                        foreach (var workerId in workerIds)
                        {
                            var worker = await context.Workers.FindAsync(workerId);
                            if (worker != null)
                            {
                                skillEntity.Workers.Add(worker);
                            }
                        }
                        await context.SaveChangesAsync();
                    }
                }

                return await _skillService.GetSkillByIdAsync(createdSkill.Id) ?? createdSkill;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Skill?> UpdateSkill(
            int id,
            string? name = null,
            string? description = null,
            List<int>? workerIds = null)
        {
            var existingSkill = await _skillService.GetSkillByIdAsync(id);
            if (existingSkill == null)
                return null;

            var updatedSkill = new Skill
            {
                Id = existingSkill.Id,
                Name = name ?? existingSkill.Name,
                Description = description ?? existingSkill.Description
            };

            var result = await _skillService.UpdateSkillAsync(id, updatedSkill, "System");

            // Update workers if specified
            if (workerIds != null)
            {
                await using var context = await _contextFactory.CreateDbContextAsync();
                var skillEntity = await context.Skills
                    .Include(s => s.Workers)
                    .FirstOrDefaultAsync(s => s.Id == id);

                if (skillEntity != null)
                {
                    // Clear existing workers
                    skillEntity.Workers.Clear();

                    // Add new workers
                    foreach (var workerId in workerIds)
                    {
                        var worker = await context.Workers.FindAsync(workerId);
                        if (worker != null)
                        {
                            skillEntity.Workers.Add(worker);
                        }
                    }
                    await context.SaveChangesAsync();
                }
            }

            return await _skillService.GetSkillByIdAsync(id);
        }

        public async Task<bool> DeleteSkill(int id)
        {
            return await _skillService.DeleteSkillAsync(id);
        }

        // Task Mutations
        public async Task<Task> CreateTask(
            string name,
            string type,
            string description,
            Shared.Enums.TaskStatus status = Shared.Enums.TaskStatus.TODO,
            Shared.Enums.TaskPriority priority = Shared.Enums.TaskPriority.MEDIUM,
            string? timeForCompletion = null,
            DateTime? dueDate = null,
            DateTime? startDate = null,
            string? category = null,
            Shared.Enums.InspectionStatus inspectionStatus = Shared.Enums.InspectionStatus.NOT_REQUIRED,
            string? associatedMethodStatement = null,
            int? chiefEngineerId = null,
            List<int>? workerIds = null,
            List<int>? equipmentIds = null)
        {
            // Validate required fields
            if (string.IsNullOrWhiteSpace(name))
            {
                throw new GraphQLException(new Error("Validation", "Task name is required."));
            }
            if (string.IsNullOrWhiteSpace(type))
            {
                throw new GraphQLException(new Error("Validation", "Task type is required."));
            }
            if (string.IsNullOrWhiteSpace(description))
            {
                throw new GraphQLException(new Error("Validation", "Task description is required."));
            }

            workerIds ??= new List<int>();
            equipmentIds ??= new List<int>();

            try
            {
                var task = new Task
                {
                    Name = name,
                    Type = type,
                    Description = description,
                    Status = status,
                    Priority = priority,
                    TimeForCompletion = timeForCompletion,
                    DueDate = dueDate,
                    StartDate = startDate,
                    Category = category,
                    InspectionStatus = inspectionStatus,
                    AssociatedMethodStatement = associatedMethodStatement,
                    ChiefEngineerId = chiefEngineerId
                };

                var createdTask = await _taskService.CreateTaskAsync(task);

                // Add relationships if specified using the relationship service
                if (workerIds.Any())
                {
                    await _relationshipService.AssignWorkersToTaskAsync(createdTask.Id, workerIds);
                }
                if (equipmentIds.Any())
                {
                    await _relationshipService.AssignEquipmentToTaskAsync(createdTask.Id, equipmentIds);
                }

                return await _taskService.GetTaskByIdAsync(createdTask.Id) ?? createdTask;
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Task?> UpdateTask(
            int id,
            string? name = null,
            string? type = null,
            string? description = null,
            Shared.Enums.TaskStatus? status = null,
            Shared.Enums.TaskPriority? priority = null,
            string? timeForCompletion = null,
            DateTime? dueDate = null,
            DateTime? startDate = null,
            string? category = null,
            Shared.Enums.InspectionStatus? inspectionStatus = null,
            string? associatedMethodStatement = null,
            int? chiefEngineerId = null,
            List<int>? workerIds = null,
            List<int>? equipmentIds = null)
        {
            var existingTask = await _taskService.GetTaskByIdAsync(id);
            if (existingTask == null)
                return null;

            var updatedTask = new Task
            {
                Id = existingTask.Id,
                Name = name ?? existingTask.Name,
                Type = type ?? existingTask.Type,
                Description = description ?? existingTask.Description,
                Status = status ?? existingTask.Status,
                TaskNumber = existingTask.TaskNumber, // Keep existing task number
                Priority = priority ?? existingTask.Priority,
                TimeForCompletion = timeForCompletion ?? existingTask.TimeForCompletion,
                DueDate = dueDate ?? existingTask.DueDate,
                StartDate = startDate ?? existingTask.StartDate,
                Category = category ?? existingTask.Category,
                InspectionStatus = inspectionStatus ?? existingTask.InspectionStatus,
                AssociatedMethodStatement = associatedMethodStatement ?? existingTask.AssociatedMethodStatement,
                ChiefEngineerId = chiefEngineerId ?? existingTask.ChiefEngineerId
            };

            var result = await _taskService.UpdateTaskAsync(id, updatedTask);

            // Update relationships if specified using the relationship service
            if (workerIds != null || equipmentIds != null)
            {
                await _relationshipService.UpdateTaskRelationshipsAsync(id, workerIds, equipmentIds);
            }

            return await _taskService.GetTaskByIdAsync(id);
        }

        public async Task<bool> DeleteTask(int id)
        {
            return await _taskService.DeleteTaskAsync(id);
        }

        // Equipment Mutations
        public async Task<Equipment> CreateEquipment(
            string name,
            string? description = null,
            string? serialNumber = null,
            string? model = null,
            string? manufacturer = null,
            DateTime? purchaseDate = null,
            DateTime? lastMaintenanceDate = null,
            DateTime? nextMaintenanceDate = null,
            string? location = null,
            string? status = null,
            decimal? purchasePrice = null,
            string? category = null)
        {
            try
            {
                var equipment = new Equipment
                {
                    Name = name,
                    Description = description,
                    SerialNumber = serialNumber,
                    Model = model,
                    Manufacturer = manufacturer,
                    PurchaseDate = purchaseDate,
                    LastMaintenanceDate = lastMaintenanceDate,
                    NextMaintenanceDate = nextMaintenanceDate,
                    Location = location,
                    Status = status ?? "Available",
                    PurchasePrice = purchasePrice,
                    Category = category
                };

                return await _equipmentService.CreateEquipmentAsync(equipment);
            }
            catch (GraphQLException)
            {
                throw;
            }
            catch (Exception ex)
            {
                throw new GraphQLException(new Error("Internal Server Error", ex.Message));
            }
        }

        public async Task<Equipment?> UpdateEquipment(
            int id,
            string? name = null,
            string? description = null,
            string? serialNumber = null,
            string? model = null,
            string? manufacturer = null,
            DateTime? purchaseDate = null,
            DateTime? lastMaintenanceDate = null,
            DateTime? nextMaintenanceDate = null,
            string? location = null,
            string? status = null,
            decimal? purchasePrice = null,
            string? category = null)
        {
            var existingEquipment = await _equipmentService.GetEquipmentByIdAsync(id);
            if (existingEquipment == null)
                return null;

            var updatedEquipment = new Equipment
            {
                Id = existingEquipment.Id,
                Name = name ?? existingEquipment.Name,
                Description = description ?? existingEquipment.Description,
                SerialNumber = serialNumber ?? existingEquipment.SerialNumber,
                Model = model ?? existingEquipment.Model,
                Manufacturer = manufacturer ?? existingEquipment.Manufacturer,
                PurchaseDate = purchaseDate ?? existingEquipment.PurchaseDate,
                LastMaintenanceDate = lastMaintenanceDate ?? existingEquipment.LastMaintenanceDate,
                NextMaintenanceDate = nextMaintenanceDate ?? existingEquipment.NextMaintenanceDate,
                Location = location ?? existingEquipment.Location,
                Status = status ?? existingEquipment.Status,
                PurchasePrice = purchasePrice ?? existingEquipment.PurchasePrice,
                Category = category ?? existingEquipment.Category
            };

            return await _equipmentService.UpdateEquipmentAsync(id, updatedEquipment);
        }

        public async Task<bool> DeleteEquipment(int id)
        {
            return await _equipmentService.DeleteEquipmentAsync(id);
        }

        // File Storage Mutations
        public async Task<FileUploadResult> UploadFile(
            IFile file,
            string bucketName,
            string? description = null,
            string? folderPath = null,
            bool generateThumbnail = true)
        {
            if (file == null)
                throw new GraphQLException(new Error("Validation", "File is required"));

            try
            {
                var request = new FileUploadRequest
                {
                    FileName = file.Name,
                    BucketName = bucketName,
                    Description = description,
                    FolderPath = folderPath,
                    GenerateThumbnail = generateThumbnail
                };

                using var stream = file.OpenReadStream();
                return await _fileStorageService.UploadFileAsync(stream, request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file {FileName} to bucket {BucketName}", file.Name, bucketName);
                throw new GraphQLException(new Error("FileUpload", $"Failed to upload file: {ex.Message}"));
            }
        }

        public async Task<object> UploadWorkerFiles(
            int workerId,
            IFile? profilePicture = null,
            IReadOnlyList<IFile>? documents = null,
            IReadOnlyList<string>? documentDescriptions = null)
        {
            try
            {
                // Validate worker exists
                var worker = await _workerService.GetWorkerByIdAsync(workerId);
                if (worker == null)
                    throw new GraphQLException(new Error("Validation", $"Worker with ID {workerId} not found"));

                var result = new
                {
                    WorkerId = workerId,
                    ProfilePictureResult = (FileUploadResult?)null,
                    DocumentResults = new List<FileUploadResult>(),
                    Success = false,
                    ErrorMessage = (string?)null
                };

                // Use transaction scope for coordinated operations
                await using var transactionScope = await _fileStorageTransactionService.CreateTransactionScopeAsync();

                try
                {
                    var profilePictureResult = (FileUploadResult?)null;
                    var documentResults = new List<FileUploadResult>();

                    // Upload profile picture if provided
                    if (profilePicture != null)
                    {
                        var profileRequest = new FileUploadRequest
                        {
                            FileName = $"worker_{workerId}_profile_{DateTime.UtcNow:yyyyMMdd_HHmmss}_{profilePicture.Name}",
                            BucketName = BucketNames.ProfilePictures,
                            Description = $"Profile picture for worker {worker.Name}",
                            FolderPath = $"worker_{workerId}",
                            GenerateThumbnail = true
                        };

                        using var profileStream = profilePicture.OpenReadStream();
                        profilePictureResult = await transactionScope.UploadFileAsync(profileStream, profileRequest);

                        // Update worker's PhotoUrl in the database
                        var dbContext = transactionScope.DbContext;
                        var workerEntity = await dbContext.Set<Worker>().FindAsync(workerId);
                        if (workerEntity != null)
                        {
                            workerEntity.PhotoUrl = profilePictureResult.AccessUrl;
                        }
                    }

                    // Upload documents if provided
                    if (documents != null && documents.Count > 0)
                    {
                        for (int i = 0; i < documents.Count; i++)
                        {
                            var document = documents[i];
                            var description = documentDescriptions != null && i < documentDescriptions.Count
                                ? documentDescriptions[i]
                                : $"Document for worker {worker.Name}";

                            var docRequest = new FileUploadRequest
                            {
                                FileName = $"worker_{workerId}_doc_{DateTime.UtcNow:yyyyMMdd_HHmmss}_{document.Name}",
                                BucketName = BucketNames.Documents,
                                Description = description,
                                FolderPath = $"worker_{workerId}",
                                GenerateThumbnail = false
                            };

                            using var docStream = document.OpenReadStream();
                            var docResult = await transactionScope.UploadFileAsync(docStream, docRequest);
                            documentResults.Add(docResult);
                        }
                    }

                    // Save database changes
                    await transactionScope.DbContext.SaveChangesAsync();

                    // Commit the transaction
                    await transactionScope.CommitAsync();

                    return new
                    {
                        WorkerId = workerId,
                        ProfilePictureResult = profilePictureResult,
                        DocumentResults = documentResults,
                        Success = true,
                        ErrorMessage = (string?)null
                    };
                }
                catch
                {
                    await transactionScope.RollbackAsync();
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading files for worker {WorkerId}", workerId);
                return new
                {
                    WorkerId = workerId,
                    ProfilePictureResult = (FileUploadResult?)null,
                    DocumentResults = new List<FileUploadResult>(),
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public async Task<bool> DeleteFile(Guid fileId)
        {
            try
            {
                return await _fileStorageService.DeleteFileAsync(fileId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file {FileId}", fileId);
                throw new GraphQLException(new Error("FileDelete", $"Failed to delete file: {ex.Message}"));
            }
        }

        public async Task<FileMetadata?> GetFileMetadata(Guid fileId)
        {
            try
            {
                return await _fileStorageService.GetFileMetadataAsync(fileId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting file metadata for {FileId}", fileId);
                throw new GraphQLException(new Error("FileMetadata", $"Failed to get file metadata: {ex.Message}"));
            }
        }
    }
}
