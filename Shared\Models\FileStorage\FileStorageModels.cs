namespace Shared.Models.FileStorage
{
    public class FileUploadRequest
    {
        public string FileName { get; set; } = string.Empty;
        public string BucketName { get; set; } = string.Empty;
        public string? Description { get; set; }
        public string? FolderPath { get; set; }
        public Dictionary<string, string>? Metadata { get; set; }
        public bool GenerateThumbnail { get; set; } = true;
        public bool CreateNewVersion { get; set; } = false;
        public Guid? OriginalFileId { get; set; }
    }

    public class FileUploadResult
    {
        public Guid FileId { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string ObjectKey { get; set; } = string.Empty;
        public string BucketName { get; set; } = string.Empty;
        public long SizeBytes { get; set; }
        public string ContentType { get; set; } = string.Empty;
        public string? ETag { get; set; }
        public string? AccessUrl { get; set; }
        public int Version { get; set; }
        public bool HasThumbnail { get; set; }
        public string? ThumbnailUrl { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class FileDownloadRequest
    {
        public Guid FileId { get; set; }
        public int? Version { get; set; }
        public bool DownloadThumbnail { get; set; } = false;
    }

    public class FileDownloadResult
    {
        public Stream FileStream { get; set; } = null!;
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long SizeBytes { get; set; }
        public string? ETag { get; set; }
    }

    public class FileMetadata
    {
        public Guid Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public long SizeBytes { get; set; }
        public string? Description { get; set; }
        public string BucketName { get; set; } = string.Empty;
        public string ObjectKey { get; set; } = string.Empty;
        public int CurrentVersion { get; set; }
        public bool IsLatestVersion { get; set; }
        public Guid? OriginalFileId { get; set; }
        public string? AccessUrl { get; set; }
        public Dictionary<string, string>? Metadata { get; set; }
        public List<ThumbnailInfo>? Thumbnails { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;
    }

    public class ThumbnailInfo
    {
        public Guid Id { get; set; }
        public string FileName { get; set; } = string.Empty;
        public int Width { get; set; }
        public int Height { get; set; }
        public long SizeBytes { get; set; }
        public string? AccessUrl { get; set; }
    }

    public class FileListRequest
    {
        public string? BucketName { get; set; }
        public string? FolderPath { get; set; }
        public string? FileNameFilter { get; set; }
        public string? ContentTypeFilter { get; set; }
        public bool IncludeVersions { get; set; } = false;
        public bool IncludeThumbnails { get; set; } = false;
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 50;
        public string? SortBy { get; set; } = "CreatedAt";
        public bool SortDescending { get; set; } = true;
    }

    public class FileListResult
    {
        public List<FileMetadata> Files { get; set; } = new();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages { get; set; }
    }

    public class BucketInfo
    {
        public string Name { get; set; } = string.Empty;
        public DateTime CreationDate { get; set; }
        public long TotalFiles { get; set; }
        public long TotalSizeBytes { get; set; }
    }

    public class FileStorageTransaction
    {
        public Guid TransactionId { get; set; } = Guid.NewGuid();
        public List<FileUploadRequest> FileUploads { get; set; } = new();
        public List<Guid> FileDeletes { get; set; } = new();
        public Dictionary<string, object>? AdditionalData { get; set; }
    }

    public class FileStorageTransactionResult
    {
        public Guid TransactionId { get; set; }
        public bool Success { get; set; }
        public List<FileUploadResult> UploadResults { get; set; } = new();
        public List<Guid> DeletedFileIds { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public Exception? Exception { get; set; }
    }
}
